# 💣 Bomberman DOM - Real-time Multiplayer Game

A real-time multiplayer Bomberman-style game built with pure DOM manipulation (no Canvas/WebGL) using a custom mini-framework and WebSockets.

## 🎮 Features

### Core Game Features
- **2-4 Player Multiplayer**: Real-time gameplay with WebSocket communication
- **Classic Bomberman Mechanics**: Bombs, explosions, destructible blocks, and power-ups
- **Lives System**: Each player starts with 3 lives
- **Power-ups**: Bomb capacity, explosion range, and speed upgrades
- **Safe Zones**: Protected spawn areas for fair gameplay

### Technical Features
- **60+ FPS Performance**: Optimized DOM rendering with requestAnimationFrame
- **Modular Architecture**: Clean separation of concerns with ES6 modules
- **Custom Mini-Framework**: Built on the included lightweight framework
- **Real-time Chat**: In-game multiplayer chat system
- **Performance Monitoring**: Built-in FPS and performance tracking
- **Responsive Design**: Works on different screen sizes

## 🚀 Quick Start

### Prerequisites
- Node.js 16+
- Modern web browser with ES6 module support

### Installation

1. **Clone or download the project**
   ```bash
   git clone <repository-url>
   cd Bomberman-DOM
   ```

2. **Install server dependencies**
   ```bash
   npm install
   ```

3. **Start the server**
   ```bash
   npm start
   ```

   For development with auto-restart:
   ```bash
   npm run dev
   ```

4. **Open the game**
   - Navigate to `http://localhost:3000` in your browser
   - Enter a nickname and click "Join Game"
   - Wait for other players or start with 2+ players