<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bomberman DOM - Multiplayer Game</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            overflow: hidden;
            height: 100vh;
        }

        #app {
            width: 100vw;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        /* Loading Screen */
        .loading-screen {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .loading-screen h1 {
            font-size: 3rem;
            margin-bottom: 2rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }

        .nickname-input {
            padding: 1rem;
            font-size: 1.2rem;
            border: none;
            border-radius: 8px;
            margin-bottom: 1rem;
            width: 300px;
            text-align: center;
        }

        .join-button {
            padding: 1rem 2rem;
            font-size: 1.2rem;
            background: #ff6b6b;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: background 0.3s;
        }

        .join-button:hover {
            background: #ff5252;
        }

        .join-button:disabled {
            background: #666;
            cursor: not-allowed;
        }

        /* Lobby Screen */
        .lobby-screen {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            height: 100vh;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        .lobby-info {
            text-align: center;
            margin-bottom: 2rem;
        }

        .player-list {
            background: rgba(255,255,255,0.1);
            padding: 2rem;
            border-radius: 12px;
            margin-bottom: 2rem;
            min-width: 300px;
        }

        .player-item {
            padding: 0.5rem;
            margin: 0.5rem 0;
            background: rgba(255,255,255,0.2);
            border-radius: 6px;
        }

        .countdown {
            font-size: 2rem;
            font-weight: bold;
            color: #ff6b6b;
        }

        /* Game Screen */
        .game-screen {
            display: flex;
            height: 100vh;
        }

        .game-area {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            background: #2c3e50;
        }

        .game-board {
            display: grid;
            grid-template-columns: repeat(15, 32px);
            grid-template-rows: repeat(13, 32px);
            gap: 1px;
            background: #34495e;
            border: 3px solid #ecf0f1;
            border-radius: 8px;
            padding: 8px;
        }

        .cell {
            width: 32px;
            height: 32px;
            position: relative;
        }

        .wall {
            background: #7f8c8d;
            border: 1px solid #95a5a6;
        }

        .block {
            background: #e67e22;
            border: 1px solid #d35400;
            border-radius: 2px;
        }

        .empty {
            background: #ecf0f1;
        }

        .player {
            position: absolute;
            width: 28px;
            height: 28px;
            border-radius: 50%;
            border: 2px solid white;
            top: 2px;
            left: 2px;
            z-index: 10;
        }

        .player-1 { background: #e74c3c; }
        .player-2 { background: #3498db; }
        .player-3 { background: #2ecc71; }
        .player-4 { background: #f39c12; }

        .bomb {
            position: absolute;
            width: 24px;
            height: 24px;
            background: #2c3e50;
            border: 2px solid #e74c3c;
            border-radius: 50%;
            top: 4px;
            left: 4px;
            z-index: 5;
            animation: bomb-pulse 0.5s infinite alternate;
        }

        @keyframes bomb-pulse {
            from { transform: scale(1); }
            to { transform: scale(1.1); }
        }

        .explosion {
            position: absolute;
            width: 32px;
            height: 32px;
            background: radial-gradient(circle, #f39c12 0%, #e67e22 50%, #d35400 100%);
            z-index: 8;
            animation: explosion-flash 0.3s;
        }

        @keyframes explosion-flash {
            0% { opacity: 0; transform: scale(0.5); }
            50% { opacity: 1; transform: scale(1.2); }
            100% { opacity: 0.8; transform: scale(1); }
        }

        .powerup {
            position: absolute;
            width: 20px;
            height: 20px;
            border-radius: 4px;
            top: 6px;
            left: 6px;
            z-index: 3;
            animation: powerup-glow 1s infinite alternate;
        }

        .powerup-bomb { background: #9b59b6; }
        .powerup-flame { background: #e74c3c; }
        .powerup-speed { background: #2ecc71; }

        @keyframes powerup-glow {
            from { box-shadow: 0 0 5px rgba(255,255,255,0.5); }
            to { box-shadow: 0 0 15px rgba(255,255,255,0.8); }
        }

        /* Sidebar */
        .sidebar {
            width: 300px;
            background: rgba(0,0,0,0.8);
            display: flex;
            flex-direction: column;
        }

        .players-info {
            padding: 1rem;
            border-bottom: 1px solid #444;
        }

        .player-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 0.5rem 0;
            padding: 0.5rem;
            background: rgba(255,255,255,0.1);
            border-radius: 4px;
        }

        .player-name {
            font-weight: bold;
        }

        .player-lives {
            color: #e74c3c;
        }

        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            padding: 1rem;
        }

        .chat-messages {
            flex: 1;
            overflow-y: auto;
            background: rgba(255,255,255,0.05);
            border-radius: 4px;
            padding: 0.5rem;
            margin-bottom: 1rem;
            max-height: 300px;
        }

        .chat-message {
            margin: 0.25rem 0;
            padding: 0.25rem;
            border-radius: 3px;
            background: rgba(255,255,255,0.1);
        }

        .chat-input {
            padding: 0.5rem;
            border: none;
            border-radius: 4px;
            background: rgba(255,255,255,0.1);
            color: white;
        }

        .chat-input::placeholder {
            color: #bbb;
        }

        /* Performance Monitor */
        .performance-monitor {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.7);
            padding: 0.5rem;
            border-radius: 4px;
            font-family: monospace;
            font-size: 0.8rem;
            z-index: 100;
        }

        /* Game Over Screen */
        .game-over-screen {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.8);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .game-over-title {
            font-size: 3rem;
            margin-bottom: 1rem;
            color: #e74c3c;
        }

        .winner-name {
            font-size: 2rem;
            margin-bottom: 2rem;
            color: #f39c12;
        }

        .restart-button {
            padding: 1rem 2rem;
            font-size: 1.2rem;
            background: #2ecc71;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
        }

        .restart-button:hover {
            background: #27ae60;
        }

        /* Hidden class */
        .hidden {
            display: none !important;
        }
    </style>
</head>
<body>
    <div id="app">
        <!-- Loading/Nickname Screen -->
        <div id="loading-screen" class="loading-screen">
            <h1>💣 Bomberman DOM</h1>
            <input type="text" id="nickname-input" class="nickname-input" placeholder="Enter your nickname" maxlength="20">
            <button id="join-button" class="join-button">Join Game</button>
            <div id="connection-status" style="margin-top: 1rem; color: #ff6b6b;"></div>
        </div>

        <!-- Lobby Screen -->
        <div id="lobby-screen" class="lobby-screen hidden">
            <div class="lobby-info">
                <h2>Waiting for Players...</h2>
                <p>Game starts when 2+ players join</p>
            </div>
            <div class="player-list" id="player-list">
                <!-- Players will be added here -->
            </div>
            <div id="countdown-display" class="countdown hidden"></div>
        </div>

        <!-- Game Screen -->
        <div id="game-screen" class="game-screen hidden">
            <div class="game-area">
                <div id="game-board" class="game-board">
                    <!-- Game cells will be generated here -->
                </div>
            </div>
            <div class="sidebar">
                <div class="players-info" id="players-info">
                    <!-- Player info will be added here -->
                </div>
                <div class="chat-container">
                    <div class="chat-messages" id="chat-messages"></div>
                    <input type="text" id="chat-input" class="chat-input" placeholder="Type a message...">
                </div>
            </div>
        </div>

        <!-- Game Over Screen -->
        <div id="game-over-screen" class="game-over-screen hidden">
            <div class="game-over-title">Game Over!</div>
            <div id="winner-display" class="winner-name"></div>
            <button id="restart-button" class="restart-button">Play Again</button>
        </div>

        <!-- Performance Monitor -->
        <div id="performance-monitor" class="performance-monitor hidden">
            <div>FPS: <span id="fps-counter">60</span></div>
            <div>Frame Time: <span id="frame-time">16.67ms</span></div>
            <div>Players: <span id="player-count">0</span></div>
        </div>
    </div>

    <script type="module" src="src/main.js"></script>
</body>
</html>
