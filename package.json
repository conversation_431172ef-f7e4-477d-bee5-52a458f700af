{"name": "bomberman-dom", "version": "1.0.0", "description": "Real-time multiplayer Bomberman game using DOM manipulation and WebSockets", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "dev": "node --watch server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["bomberman", "multiplayer", "websocket", "dom", "game", "realtime"], "author": "Bomberman DOM Team", "license": "MIT", "dependencies": {"ws": "^8.14.2"}, "devDependencies": {}, "engines": {"node": ">=16.0.0"}}