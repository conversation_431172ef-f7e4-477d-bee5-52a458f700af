<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bomberman Test</title>
</head>
<body>
    <h1>Bomberman DOM Test</h1>
    <div id="test-output"></div>
    
    <script type="module">
        console.log('Test script loading...');
        
        try {
            // Test basic imports
            import { GAME_CONFIG } from './src/config/constants.js';
            console.log('Constants loaded:', GAME_CONFIG);
            
            import { gameState } from './src/core/GameState.js';
            console.log('GameState loaded:', gameState);
            
            import { inputManager } from './src/input/InputManager.js';
            console.log('InputManager loaded:', inputManager);
            
            document.getElementById('test-output').innerHTML = `
                <p>✅ Constants loaded</p>
                <p>✅ GameState loaded</p>
                <p>✅ InputManager loaded</p>
                <p>Game Config: ${JSON.stringify(GAME_CONFIG, null, 2)}</p>
            `;
            
        } catch (error) {
            console.error('Test failed:', error);
            document.getElementById('test-output').innerHTML = `
                <p>❌ Test failed: ${error.message}</p>
                <pre>${error.stack}</pre>
            `;
        }
    </script>
</body>
</html>
