/**
 * Bomberman WebSocket Server
 * Handles real-time multiplayer communication, rooms, and game state synchronization
 */

import { WebSocketServer, WebSocket } from 'ws';
import http from 'http';
import path from 'path';
import fs from 'fs';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Game constants
const GAME_CONFIG = {
    MAX_PLAYERS: 4,
    MIN_PLAYERS: 2,
    LOBBY_TIMEOUT: 20000, // 20 seconds
    COUNTDOWN_TIME: 10000, // 10 seconds
    MAP_WIDTH: 15,
    MAP_HEIGHT: 13,
    BOMB_TIMER: 3000, // 3 seconds
    EXPLOSION_DURATION: 500 // 0.5 seconds
};

// Game state
class GameRoom {
    constructor(id) {
        this.id = id;
        this.players = new Map();
        this.gameState = 'lobby'; // lobby, countdown, playing, finished
        this.gameData = null;
        this.countdownTimer = null;
        this.lobbyTimer = null;
        this.chatHistory = [];
        this.createdAt = Date.now();
    }

    addPlayer(playerId, nickname, ws) {
        if (this.players.size >= GAME_CONFIG.MAX_PLAYERS) {
            return false;
        }

        const playerData = {
            id: playerId,
            nickname: nickname,
            ws: ws,
            x: 0,
            y: 0,
            lives: 3,
            bombCapacity: 1,
            bombRange: 1,
            speed: 1,
            powerups: [],
            isAlive: true,
            joinedAt: Date.now()
        };

        this.players.set(playerId, playerData);
        this.assignSpawnPosition(playerData);
        
        // Start lobby timer if this is the first player
        if (this.players.size === 1) {
            this.startLobbyTimer();
        }

        // Check if we should start countdown
        if (this.players.size >= GAME_CONFIG.MIN_PLAYERS && this.gameState === 'lobby') {
            this.startCountdown();
        }

        return true;
    }

    removePlayer(playerId) {
        const player = this.players.get(playerId);
        if (player) {
            this.players.delete(playerId);
            
            // If game is in progress and player was alive, check for game end
            if (this.gameState === 'playing' && player.isAlive) {
                this.checkGameEnd();
            }

            // Cancel timers if no players left
            if (this.players.size === 0) {
                this.clearTimers();
            }
        }
    }

    assignSpawnPosition(player) {
        const positions = [
            { x: 1, y: 1 },     // Top-left
            { x: 13, y: 1 },    // Top-right
            { x: 1, y: 11 },    // Bottom-left
            { x: 13, y: 11 }    // Bottom-right
        ];
        
        const playerIndex = Array.from(this.players.keys()).indexOf(player.id);
        if (playerIndex < positions.length) {
            player.x = positions[playerIndex].x;
            player.y = positions[playerIndex].y;
        }
    }

    startLobbyTimer() {
        this.lobbyTimer = setTimeout(() => {
            if (this.players.size >= GAME_CONFIG.MIN_PLAYERS && this.gameState === 'lobby') {
                this.startCountdown();
            }
        }, GAME_CONFIG.LOBBY_TIMEOUT);
    }

    startCountdown() {
        if (this.gameState !== 'lobby') return;
        
        this.gameState = 'countdown';
        this.clearTimers();
        
        this.broadcast({
            type: 'countdown_start',
            duration: GAME_CONFIG.COUNTDOWN_TIME
        });

        this.countdownTimer = setTimeout(() => {
            this.startGame();
        }, GAME_CONFIG.COUNTDOWN_TIME);
    }

    startGame() {
        this.gameState = 'playing';
        this.initializeGameData();
        
        this.broadcast({
            type: 'game_start',
            gameData: this.gameData,
            players: this.getPlayersData()
        });
    }

    initializeGameData() {
        // Initialize map with walls and blocks
        this.gameData = {
            map: this.generateMap(),
            bombs: new Map(),
            explosions: new Map(),
            powerups: new Map(),
            startTime: Date.now()
        };
    }

    generateMap() {
        const map = Array(GAME_CONFIG.MAP_HEIGHT).fill().map(() => 
            Array(GAME_CONFIG.MAP_WIDTH).fill('empty')
        );

        // Add walls (border and internal pattern)
        for (let y = 0; y < GAME_CONFIG.MAP_HEIGHT; y++) {
            for (let x = 0; x < GAME_CONFIG.MAP_WIDTH; x++) {
                // Border walls
                if (x === 0 || x === GAME_CONFIG.MAP_WIDTH - 1 || 
                    y === 0 || y === GAME_CONFIG.MAP_HEIGHT - 1) {
                    map[y][x] = 'wall';
                }
                // Internal walls (every other position)
                else if (x % 2 === 0 && y % 2 === 0) {
                    map[y][x] = 'wall';
                }
            }
        }

        // Add destructible blocks (randomly, avoiding spawn areas)
        const spawnAreas = [
            {x: 1, y: 1}, {x: 2, y: 1}, {x: 1, y: 2},     // Top-left safe zone
            {x: 13, y: 1}, {x: 12, y: 1}, {x: 13, y: 2},  // Top-right safe zone
            {x: 1, y: 11}, {x: 2, y: 11}, {x: 1, y: 10},  // Bottom-left safe zone
            {x: 13, y: 11}, {x: 12, y: 11}, {x: 13, y: 10} // Bottom-right safe zone
        ];

        for (let y = 1; y < GAME_CONFIG.MAP_HEIGHT - 1; y++) {
            for (let x = 1; x < GAME_CONFIG.MAP_WIDTH - 1; x++) {
                if (map[y][x] === 'empty') {
                    // Check if position is in spawn area
                    const isSpawnArea = spawnAreas.some(pos => pos.x === x && pos.y === y);
                    
                    if (!isSpawnArea && Math.random() < 0.6) {
                        map[y][x] = 'block';
                    }
                }
            }
        }

        return map;
    }

    checkGameEnd() {
        const alivePlayers = Array.from(this.players.values()).filter(p => p.isAlive);
        
        if (alivePlayers.length <= 1) {
            this.gameState = 'finished';
            const winner = alivePlayers.length === 1 ? alivePlayers[0] : null;
            
            this.broadcast({
                type: 'game_end',
                winner: winner ? { id: winner.id, nickname: winner.nickname } : null
            });
        }
    }

    addChatMessage(playerId, message) {
        const player = this.players.get(playerId);
        if (!player) return;

        const chatMessage = {
            id: Date.now(),
            playerId: playerId,
            nickname: player.nickname,
            message: message.trim(),
            timestamp: Date.now()
        };

        this.chatHistory.push(chatMessage);
        
        // Keep only last 50 messages
        if (this.chatHistory.length > 50) {
            this.chatHistory = this.chatHistory.slice(-50);
        }

        this.broadcast({
            type: 'chat_message',
            message: chatMessage
        });
    }

    getPlayersData() {
        return Array.from(this.players.values()).map(player => ({
            id: player.id,
            nickname: player.nickname,
            x: player.x,
            y: player.y,
            lives: player.lives,
            bombCapacity: player.bombCapacity,
            bombRange: player.bombRange,
            speed: player.speed,
            isAlive: player.isAlive
        }));
    }

    broadcast(message, excludePlayerId = null) {
        this.players.forEach((player, playerId) => {
            if (playerId !== excludePlayerId && player.ws.readyState === WebSocket.OPEN) {
                try {
                    player.ws.send(JSON.stringify(message));
                } catch (error) {
                    console.error('Error broadcasting to player:', playerId, error);
                }
            }
        });
    }

    clearTimers() {
        if (this.lobbyTimer) {
            clearTimeout(this.lobbyTimer);
            this.lobbyTimer = null;
        }
        if (this.countdownTimer) {
            clearTimeout(this.countdownTimer);
            this.countdownTimer = null;
        }
    }
}

// Server state
const rooms = new Map();
const playerRooms = new Map(); // playerId -> roomId

// Create HTTP server for serving static files
const server = http.createServer((req, res) => {
    console.log('HTTP Request:', req.method, req.url);

    let filePath = req.url === '/' ? '/index.html' : req.url;
    const extname = path.extname(filePath);

    let contentType = 'text/html';
    switch (extname) {
        case '.js': contentType = 'text/javascript'; break;
        case '.css': contentType = 'text/css'; break;
        case '.json': contentType = 'application/json'; break;
    }

    const fullPath = path.join(__dirname, filePath);
    console.log('Serving file:', fullPath);

    fs.readFile(fullPath, (err, content) => {
        if (err) {
            console.error('File error:', err.code, fullPath);
            if (err.code === 'ENOENT') {
                res.writeHead(404);
                res.end('File not found');
            } else {
                res.writeHead(500);
                res.end('Server error');
            }
        } else {
            console.log('File served successfully:', filePath);
            res.writeHead(200, { 'Content-Type': contentType });
            res.end(content, 'utf-8');
        }
    });
});

// Create WebSocket server
const wss = new WebSocketServer({ server });

// Find or create a room for a player
function findAvailableRoom() {
    // Find a room in lobby state with space
    for (const room of rooms.values()) {
        if (room.gameState === 'lobby' && room.players.size < GAME_CONFIG.MAX_PLAYERS) {
            return room;
        }
    }
    
    // Create new room
    const roomId = 'room_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11);
    const room = new GameRoom(roomId);
    rooms.set(roomId, room);
    return room;
}

// WebSocket connection handling
wss.on('connection', (ws) => {
    let playerId = null;
    let currentRoom = null;

    console.log('New WebSocket connection');

    ws.on('message', (data) => {
        try {
            const message = JSON.parse(data);
            
            switch (message.type) {
                case 'join_game':
                    handleJoinGame(message);
                    break;
                case 'chat_message':
                    handleChatMessage(message);
                    break;
                case 'player_move':
                    handlePlayerMove(message);
                    break;
                case 'place_bomb':
                    handlePlaceBomb(message);
                    break;
                case 'restart_game':
                    handleRestartGame(message);
                    break;
                case 'ping':
                    // Heartbeat ping - respond with pong
                    ws.send(JSON.stringify({ type: 'pong' }));
                    break;
                default:
                    console.log('Unknown message type:', message.type);
            }
        } catch (error) {
            console.error('Error parsing message:', error);
        }
    });

    ws.on('close', () => {
        if (playerId && currentRoom) {
            currentRoom.removePlayer(playerId);
            playerRooms.delete(playerId);
            
            // Clean up empty rooms
            if (currentRoom.players.size === 0) {
                rooms.delete(currentRoom.id);
            } else {
                // Notify other players
                currentRoom.broadcast({
                    type: 'player_left',
                    playerId: playerId,
                    players: currentRoom.getPlayersData()
                });
            }
        }
        console.log('WebSocket connection closed');
    });

    function handleJoinGame(message) {
        const nickname = message.nickname?.trim();
        if (!nickname || nickname.length === 0) {
            ws.send(JSON.stringify({ type: 'error', message: 'Invalid nickname' }));
            return;
        }

        playerId = 'player_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11);
        currentRoom = findAvailableRoom();
        
        if (currentRoom.addPlayer(playerId, nickname, ws)) {
            playerRooms.set(playerId, currentRoom.id);
            
            // Send join confirmation
            ws.send(JSON.stringify({
                type: 'join_success',
                playerId: playerId,
                roomId: currentRoom.id,
                gameState: currentRoom.gameState
            }));

            // Broadcast player list update
            currentRoom.broadcast({
                type: 'players_update',
                players: currentRoom.getPlayersData(),
                gameState: currentRoom.gameState
            });

            // Send chat history
            ws.send(JSON.stringify({
                type: 'chat_history',
                messages: currentRoom.chatHistory
            }));
        } else {
            ws.send(JSON.stringify({ type: 'error', message: 'Room is full' }));
        }
    }

    function handleChatMessage(message) {
        if (currentRoom && playerId) {
            currentRoom.addChatMessage(playerId, message.message);
        }
    }

    function handlePlayerMove(message) {
        if (currentRoom && playerId && currentRoom.gameState === 'playing') {
            const player = currentRoom.players.get(playerId);
            if (player && player.isAlive) {
                // Validate and update player position
                const newX = Math.max(0, Math.min(GAME_CONFIG.MAP_WIDTH - 1, message.x));
                const newY = Math.max(0, Math.min(GAME_CONFIG.MAP_HEIGHT - 1, message.y));
                
                player.x = newX;
                player.y = newY;
                
                // Broadcast position update
                currentRoom.broadcast({
                    type: 'player_position',
                    playerId: playerId,
                    x: newX,
                    y: newY
                });
            }
        }
    }

    function handlePlaceBomb() {
        if (currentRoom && playerId && currentRoom.gameState === 'playing') {
            const player = currentRoom.players.get(playerId);
            if (player && player.isAlive) {
                const bombId = 'bomb_' + Date.now() + '_' + playerId;
                const bomb = {
                    id: bombId,
                    x: player.x,
                    y: player.y,
                    playerId: playerId,
                    range: player.bombRange,
                    timer: GAME_CONFIG.BOMB_TIMER,
                    placedAt: Date.now()
                };
                
                currentRoom.gameData.bombs.set(bombId, bomb);
                
                // Broadcast bomb placement
                currentRoom.broadcast({
                    type: 'bomb_placed',
                    bomb: bomb
                });
                
                // Set explosion timer
                setTimeout(() => {
                    if (currentRoom.gameData.bombs.has(bombId)) {
                        explodeBomb(currentRoom, bombId);
                    }
                }, GAME_CONFIG.BOMB_TIMER);
            }
        }
    }

    function handleRestartGame() {
        if (currentRoom && playerId) {
            // Reset room to lobby state
            currentRoom.gameState = 'lobby';
            currentRoom.gameData = null;
            currentRoom.clearTimers();
            
            // Reset all players
            currentRoom.players.forEach(player => {
                player.lives = 3;
                player.bombCapacity = 1;
                player.bombRange = 1;
                player.speed = 1;
                player.isAlive = true;
                currentRoom.assignSpawnPosition(player);
            });
            
            // Broadcast restart
            currentRoom.broadcast({
                type: 'game_restart',
                players: currentRoom.getPlayersData()
            });
            
            // Start countdown if enough players
            if (currentRoom.players.size >= GAME_CONFIG.MIN_PLAYERS) {
                currentRoom.startCountdown();
            }
        }
    }
});

function explodeBomb(room, bombId) {
    const bomb = room.gameData.bombs.get(bombId);
    if (!bomb) return;
    
    room.gameData.bombs.delete(bombId);
    
    // Create explosion pattern
    const explosions = [];
    const directions = [
        { dx: 0, dy: 0 },   // Center
        { dx: 1, dy: 0 },   // Right
        { dx: -1, dy: 0 },  // Left
        { dx: 0, dy: 1 },   // Down
        { dx: 0, dy: -1 }   // Up
    ];
    
    directions.forEach(dir => {
        for (let i = 0; i < (dir.dx === 0 && dir.dy === 0 ? 1 : bomb.range); i++) {
            const x = bomb.x + dir.dx * i;
            const y = bomb.y + dir.dy * i;
            
            if (x < 0 || x >= GAME_CONFIG.MAP_WIDTH || y < 0 || y >= GAME_CONFIG.MAP_HEIGHT) {
                break;
            }
            
            const cell = room.gameData.map[y][x];
            if (cell === 'wall') {
                break;
            }
            
            explosions.push({ x, y });
            
            if (cell === 'block') {
                room.gameData.map[y][x] = 'empty';
                // Chance to spawn power-up
                if (Math.random() < 0.3) {
                    const powerupTypes = ['bomb', 'flame', 'speed'];
                    const powerupType = powerupTypes[Math.floor(Math.random() * powerupTypes.length)];
                    const powerupId = 'powerup_' + Date.now() + '_' + x + '_' + y;
                    room.gameData.powerups.set(powerupId, {
                        id: powerupId,
                        type: powerupType,
                        x: x,
                        y: y
                    });
                }
                break;
            }
        }
    });
    
    // Check for player damage
    room.players.forEach(player => {
        if (player.isAlive) {
            const playerHit = explosions.some(exp => exp.x === player.x && exp.y === player.y);
            if (playerHit) {
                player.lives--;
                if (player.lives <= 0) {
                    player.isAlive = false;
                }
            }
        }
    });
    
    // Broadcast explosion
    room.broadcast({
        type: 'explosion',
        bombId: bombId,
        explosions: explosions,
        mapUpdate: room.gameData.map,
        powerups: Array.from(room.gameData.powerups.values()),
        players: room.getPlayersData()
    });
    
    // Remove explosion after duration
    setTimeout(() => {
        room.broadcast({
            type: 'explosion_end',
            explosions: explosions
        });
    }, GAME_CONFIG.EXPLOSION_DURATION);
    
    // Check for game end
    room.checkGameEnd();
}

// Start server
const PORT = process.env.PORT || 3000;
server.listen(PORT, () => {
    console.log(`Bomberman server running on port ${PORT}`);
    console.log(`Game supports ${GAME_CONFIG.MIN_PLAYERS}-${GAME_CONFIG.MAX_PLAYERS} players`);
});

// Cleanup on exit
process.on('SIGINT', () => {
    console.log('Shutting down server...');
    rooms.forEach(room => room.clearTimers());
    server.close(() => {
        console.log('Server closed');
        process.exit(0);
    });
});
