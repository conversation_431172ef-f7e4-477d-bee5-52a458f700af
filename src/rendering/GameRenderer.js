/**
 * Game Renderer
 * Handles DOM-based rendering of all game elements
 */

import { h, vdom } from '../../mini-framework/src/framework.js';
import { GAME_CONFIG, CELL_TYPES, PLAYER_COLORS } from '../config/constants.js';
import { gameState } from '../core/GameState.js';

export class GameRenderer {
    constructor() {
        this.gameBoard = null;
        this.cellElements = new Map(); // Cache cell elements for performance
        this.playerElements = new Map();
        this.bombElements = new Map();
        this.explosionElements = new Map();
        this.powerupElements = new Map();
        this.lastRenderTime = 0;
        this.renderCount = 0;
    }

    /**
     * Initialize the renderer with the game board element
     */
    initialize(gameBoardElement) {
        this.gameBoard = gameBoardElement;
        this.setupGameBoard();
    }

    /**
     * Setup the game board grid
     */
    setupGameBoard() {
        if (!this.gameBoard) return;

        // Clear existing content
        this.gameBoard.innerHTML = '';
        this.cellElements.clear();

        // Create grid cells
        for (let y = 0; y < GAME_CONFIG.MAP_HEIGHT; y++) {
            for (let x = 0; x < GAME_CONFIG.MAP_WIDTH; x++) {
                const cell = this.createCellElement(x, y);
                this.gameBoard.appendChild(cell);
                this.cellElements.set(`${x},${y}`, cell);
            }
        }
    }

    /**
     * Create a cell element
     */
    createCellElement(x, y) {
        const cell = document.createElement('div');
        cell.className = 'cell empty';
        cell.dataset.x = x;
        cell.dataset.y = y;
        cell.style.gridColumn = x + 1;
        cell.style.gridRow = y + 1;
        return cell;
    }

    /**
     * Render the complete game state
     */
    render() {
        const startTime = performance.now();

        // Render map
        this.renderMap();

        // Render game entities
        this.renderPlayers();
        this.renderBombs();
        this.renderExplosions();
        this.renderPowerups();

        // Update performance metrics
        const renderTime = performance.now() - startTime;
        this.updatePerformanceMetrics(renderTime);

        this.renderCount++;
    }

    /**
     * Render the map (walls, blocks, empty spaces)
     */
    renderMap() {
        const map = gameState.getMap();
        if (!map || !map.length) return;

        for (let y = 0; y < map.length; y++) {
            for (let x = 0; x < map[y].length; x++) {
                const cellKey = `${x},${y}`;
                const cellElement = this.cellElements.get(cellKey);
                if (!cellElement) continue;

                const cellType = map[y][x];
                this.updateCellAppearance(cellElement, cellType);
            }
        }
    }

    /**
     * Update cell appearance based on type
     */
    updateCellAppearance(cellElement, cellType) {
        // Remove existing type classes
        cellElement.classList.remove('empty', 'wall', 'block');
        
        // Add new type class
        cellElement.classList.add(cellType);

        // Clear any existing content for empty cells
        if (cellType === CELL_TYPES.EMPTY) {
            // Remove any non-player/bomb/powerup elements
            const children = Array.from(cellElement.children);
            children.forEach(child => {
                if (!child.classList.contains('player') && 
                    !child.classList.contains('bomb') && 
                    !child.classList.contains('powerup') &&
                    !child.classList.contains('explosion')) {
                    child.remove();
                }
            });
        }
    }

    /**
     * Render all players
     */
    renderPlayers() {
        const players = gameState.getPlayers();
        const currentPlayerElements = new Set();

        // Update existing players and create new ones
        for (const [playerId, player] of players) {
            currentPlayerElements.add(playerId);
            this.renderPlayer(player);
        }

        // Remove players that no longer exist
        for (const [playerId, element] of this.playerElements) {
            if (!currentPlayerElements.has(playerId)) {
                element.remove();
                this.playerElements.delete(playerId);
            }
        }
    }

    /**
     * Render a single player
     */
    renderPlayer(player) {
        let playerElement = this.playerElements.get(player.id);
        
        if (!playerElement) {
            playerElement = this.createPlayerElement(player);
            this.playerElements.set(player.id, playerElement);
        }

        // Update player position and appearance
        this.updatePlayerElement(playerElement, player);
    }

    /**
     * Create a player element
     */
    createPlayerElement(player) {
        const playerElement = document.createElement('div');
        playerElement.className = `player player-${player.id}`;
        playerElement.dataset.playerId = player.id;
        
        // Set player color
        const colorIndex = Array.from(gameState.getPlayers().keys()).indexOf(player.id);
        playerElement.style.backgroundColor = PLAYER_COLORS[colorIndex % PLAYER_COLORS.length];
        
        return playerElement;
    }

    /**
     * Update player element position and state
     */
    updatePlayerElement(playerElement, player) {
        // Update position
        const cellKey = `${Math.round(player.x)},${Math.round(player.y)}`;
        const targetCell = this.cellElements.get(cellKey);
        
        if (targetCell && playerElement.parentNode !== targetCell) {
            // Remove from old cell
            if (playerElement.parentNode) {
                playerElement.parentNode.removeChild(playerElement);
            }
            // Add to new cell
            targetCell.appendChild(playerElement);
        }

        // Update visual state
        playerElement.classList.toggle('dead', !player.isAlive);
        playerElement.style.opacity = player.isAlive ? '1' : '0.3';
        
        // Update player info (lives, etc.)
        playerElement.title = `${player.nickname} - Lives: ${player.lives}`;
    }

    /**
     * Render all bombs
     */
    renderBombs() {
        const bombs = gameState.getBombs();
        const currentBombElements = new Set();

        // Update existing bombs and create new ones
        for (const [bombId, bomb] of bombs) {
            currentBombElements.add(bombId);
            this.renderBomb(bomb);
        }

        // Remove bombs that no longer exist
        for (const [bombId, element] of this.bombElements) {
            if (!currentBombElements.has(bombId)) {
                element.remove();
                this.bombElements.delete(bombId);
            }
        }
    }

    /**
     * Render a single bomb
     */
    renderBomb(bomb) {
        let bombElement = this.bombElements.get(bomb.id);
        
        if (!bombElement) {
            bombElement = this.createBombElement(bomb);
            this.bombElements.set(bomb.id, bombElement);
        }

        this.updateBombElement(bombElement, bomb);
    }

    /**
     * Create a bomb element
     */
    createBombElement(bomb) {
        const bombElement = document.createElement('div');
        bombElement.className = 'bomb';
        bombElement.dataset.bombId = bomb.id;
        return bombElement;
    }

    /**
     * Update bomb element
     */
    updateBombElement(bombElement, bomb) {
        // Position bomb in correct cell
        const cellKey = `${bomb.x},${bomb.y}`;
        const targetCell = this.cellElements.get(cellKey);
        
        if (targetCell && bombElement.parentNode !== targetCell) {
            if (bombElement.parentNode) {
                bombElement.parentNode.removeChild(bombElement);
            }
            targetCell.appendChild(bombElement);
        }

        // Update visual state based on timer
        const timeRatio = bomb.timer / GAME_CONFIG.BOMB_TIMER;
        if (timeRatio < 0.3) {
            bombElement.classList.add('critical');
        } else if (timeRatio < 0.6) {
            bombElement.classList.add('warning');
        }
    }

    /**
     * Render all explosions
     */
    renderExplosions() {
        const explosions = gameState.getExplosions();
        
        // Clear existing explosions
        this.clearExplosions();

        // Render new explosions
        for (const [explosionId, explosion] of explosions) {
            this.renderExplosion(explosion);
        }
    }

    /**
     * Render a single explosion
     */
    renderExplosion(explosion) {
        const explosionElement = document.createElement('div');
        explosionElement.className = 'explosion';
        explosionElement.dataset.explosionId = explosion.id || 'temp';

        // Position explosion
        const cellKey = `${explosion.x},${explosion.y}`;
        const targetCell = this.cellElements.get(cellKey);
        
        if (targetCell) {
            targetCell.appendChild(explosionElement);
            this.explosionElements.set(explosion.id || `temp_${explosion.x}_${explosion.y}`, explosionElement);
        }
    }

    /**
     * Clear all explosion elements
     */
    clearExplosions() {
        for (const [explosionId, element] of this.explosionElements) {
            element.remove();
        }
        this.explosionElements.clear();
    }

    /**
     * Render all power-ups
     */
    renderPowerups() {
        const powerups = gameState.getPowerups();
        const currentPowerupElements = new Set();

        // Update existing power-ups and create new ones
        for (const [powerupId, powerup] of powerups) {
            if (!powerup.isCollected) {
                currentPowerupElements.add(powerupId);
                this.renderPowerup(powerup);
            }
        }

        // Remove power-ups that no longer exist
        for (const [powerupId, element] of this.powerupElements) {
            if (!currentPowerupElements.has(powerupId)) {
                element.remove();
                this.powerupElements.delete(powerupId);
            }
        }
    }

    /**
     * Render a single power-up
     */
    renderPowerup(powerup) {
        let powerupElement = this.powerupElements.get(powerup.id);
        
        if (!powerupElement) {
            powerupElement = this.createPowerupElement(powerup);
            this.powerupElements.set(powerup.id, powerupElement);
        }

        this.updatePowerupElement(powerupElement, powerup);
    }

    /**
     * Create a power-up element
     */
    createPowerupElement(powerup) {
        const powerupElement = document.createElement('div');
        powerupElement.className = `powerup powerup-${powerup.type}`;
        powerupElement.dataset.powerupId = powerup.id;
        powerupElement.title = powerup.getEffectDescription();
        return powerupElement;
    }

    /**
     * Update power-up element
     */
    updatePowerupElement(powerupElement, powerup) {
        // Position power-up in correct cell
        const cellKey = `${powerup.x},${powerup.y}`;
        const targetCell = this.cellElements.get(cellKey);
        
        if (targetCell && powerupElement.parentNode !== targetCell) {
            if (powerupElement.parentNode) {
                powerupElement.parentNode.removeChild(powerupElement);
            }
            targetCell.appendChild(powerupElement);
        }
    }

    /**
     * Update performance metrics
     */
    updatePerformanceMetrics(renderTime) {
        const now = performance.now();
        const deltaTime = now - this.lastRenderTime;
        const fps = deltaTime > 0 ? 1000 / deltaTime : 60;
        
        gameState.updatePerformance(fps, renderTime);
        this.lastRenderTime = now;
    }

    /**
     * Get render statistics
     */
    getRenderStats() {
        return {
            renderCount: this.renderCount,
            cellCount: this.cellElements.size,
            playerCount: this.playerElements.size,
            bombCount: this.bombElements.size,
            explosionCount: this.explosionElements.size,
            powerupCount: this.powerupElements.size
        };
    }

    /**
     * Clear all rendered elements
     */
    clear() {
        if (this.gameBoard) {
            this.gameBoard.innerHTML = '';
        }
        
        this.cellElements.clear();
        this.playerElements.clear();
        this.bombElements.clear();
        this.explosionElements.clear();
        this.powerupElements.clear();
    }

    /**
     * Resize renderer (if needed)
     */
    resize() {
        // Recalculate cell sizes if needed
        // This is handled by CSS grid, so usually no action needed
    }
}

// Create singleton instance
export const gameRenderer = new GameRenderer();
