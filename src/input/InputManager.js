/**
 * Input Manager
 * Handles keyboard input and player controls
 */

import { domEvents } from '../../mini-framework/src/framework.js';
import { KEY_CODES, DIRECTIONS } from '../config/constants.js';
import { gameState } from '../core/GameState.js';
import { webSocketClient } from '../network/WebSocketClient.js';
import { throttle } from '../utils/helpers.js';

export class InputManager {
    constructor() {
        this.keysPressed = new Set();
        this.keyBindings = new Map();
        this.isEnabled = true;
        this.lastMoveTime = 0;
        this.moveThrottle = 150; // Minimum time between moves in ms
        
        this.setupDefaultBindings();
        this.setupEventListeners();
        
        // Throttled movement function to prevent spam
        this.throttledMove = throttle(this.handleMovement.bind(this), this.moveThrottle);
    }

    /**
     * Setup default key bindings
     */
    setupDefaultBindings() {
        // Player 1 controls (WASD + Space)
        this.keyBindings.set(KEY_CODES.W, { action: 'move', direction: DIRECTIONS.UP });
        this.keyBindings.set(KEY_CODES.S, { action: 'move', direction: DIRECTIONS.DOWN });
        this.keyBindings.set(KEY_CODES.A, { action: 'move', direction: DIRECTIONS.LEFT });
        this.keyBindings.set(KEY_CODES.D, { action: 'move', direction: DIRECTIONS.RIGHT });
        this.keyBindings.set(KEY_CODES.SPACE, { action: 'bomb' });

        // Alternative controls (Arrow keys + Enter)
        this.keyBindings.set(KEY_CODES.ARROW_UP, { action: 'move', direction: DIRECTIONS.UP });
        this.keyBindings.set(KEY_CODES.ARROW_DOWN, { action: 'move', direction: DIRECTIONS.DOWN });
        this.keyBindings.set(KEY_CODES.ARROW_LEFT, { action: 'move', direction: DIRECTIONS.LEFT });
        this.keyBindings.set(KEY_CODES.ARROW_RIGHT, { action: 'move', direction: DIRECTIONS.RIGHT });
        this.keyBindings.set(KEY_CODES.ENTER, { action: 'bomb' });

        // Special keys
        this.keyBindings.set(KEY_CODES.ESCAPE, { action: 'menu' });
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Use the framework's DOM event manager
        domEvents.bind(document, 'keydown', this.handleKeyDown.bind(this));
        domEvents.bind(document, 'keyup', this.handleKeyUp.bind(this));

        // Prevent default behavior for game keys
        domEvents.bind(document, 'keydown', (event) => {
            if (this.keyBindings.has(event.originalEvent.keyCode) && this.isEnabled) {
                event.preventDefault();
            }
        });

        // Handle window focus/blur to reset key states
        window.addEventListener('blur', () => {
            this.keysPressed.clear();
        });

        window.addEventListener('focus', () => {
            this.keysPressed.clear();
        });
    }

    /**
     * Handle key down events
     */
    handleKeyDown(event) {
        if (!this.isEnabled) return;

        const keyCode = event.originalEvent.keyCode;

        // Avoid key repeat
        if (this.keysPressed.has(keyCode)) return;

        this.keysPressed.add(keyCode);

        const binding = this.keyBindings.get(keyCode);
        if (binding) {
            this.executeAction(binding, event);
        }
    }

    /**
     * Handle key up events
     */
    handleKeyUp(event) {
        const keyCode = event.originalEvent.keyCode;
        this.keysPressed.delete(keyCode);
    }

    /**
     * Execute an action based on key binding
     */
    executeAction(binding, event) {
        if (!gameState.isGameInProgress()) {
            // Handle non-game actions
            if (binding.action === 'menu') {
                this.handleMenuAction();
            }
            return;
        }

        const localPlayer = gameState.getLocalPlayer();
        if (!localPlayer || !localPlayer.isAlive) return;

        switch (binding.action) {
            case 'move':
                this.throttledMove(binding.direction);
                break;
            case 'bomb':
                this.handleBombAction();
                break;
            case 'menu':
                this.handleMenuAction();
                break;
        }
    }

    /**
     * Handle movement action
     */
    handleMovement(direction) {
        const now = Date.now();
        if (now - this.lastMoveTime < this.moveThrottle) {
            return;
        }

        const localPlayer = gameState.getLocalPlayer();
        if (!localPlayer || !localPlayer.isAlive) return;

        const newX = localPlayer.x + direction.x;
        const newY = localPlayer.y + direction.y;

        // Basic client-side validation
        const map = gameState.getMap();
        if (this.canMoveTo(newX, newY, map)) {
            // Update local position immediately for responsiveness
            gameState.updatePlayer(localPlayer.id, { x: newX, y: newY });
            
            // Send to server
            webSocketClient.sendPlayerMove(newX, newY);
            
            this.lastMoveTime = now;
        }
    }

    /**
     * Check if player can move to a position
     */
    canMoveTo(x, y, map) {
        // Check bounds
        if (x < 0 || x >= map[0].length || y < 0 || y >= map.length) {
            return false;
        }

        // Check for walls and blocks
        if (map[y][x] === 'wall' || map[y][x] === 'block') {
            return false;
        }

        // Check for other players (optional collision)
        const players = gameState.getPlayers();
        for (const player of players.values()) {
            if (player.id !== gameState.getPlayerId() && 
                Math.round(player.x) === x && Math.round(player.y) === y) {
                return false;
            }
        }

        return true;
    }

    /**
     * Handle bomb placement action
     */
    handleBombAction() {
        if (!gameState.canPlaceBomb()) return;

        const localPlayer = gameState.getLocalPlayer();
        if (!localPlayer || !localPlayer.isAlive) return;

        // Check if there's already a bomb at this position
        const bombs = gameState.getBombs();
        const playerPos = { x: Math.round(localPlayer.x), y: Math.round(localPlayer.y) };
        
        for (const bomb of bombs.values()) {
            if (bomb.x === playerPos.x && bomb.y === playerPos.y) {
                return; // Can't place bomb on existing bomb
            }
        }

        // Send bomb placement to server
        webSocketClient.sendPlaceBomb();
    }

    /**
     * Handle menu/escape action
     */
    handleMenuAction() {
        // Toggle performance monitor or show menu
        gameState.togglePerformanceMonitor();
    }

    /**
     * Check if a key is currently pressed
     */
    isKeyPressed(keyCode) {
        return this.keysPressed.has(keyCode);
    }

    /**
     * Check if any movement key is pressed
     */
    isMovementKeyPressed() {
        const movementKeys = [
            KEY_CODES.W, KEY_CODES.A, KEY_CODES.S, KEY_CODES.D,
            KEY_CODES.ARROW_UP, KEY_CODES.ARROW_DOWN, 
            KEY_CODES.ARROW_LEFT, KEY_CODES.ARROW_RIGHT
        ];
        
        return movementKeys.some(key => this.keysPressed.has(key));
    }

    /**
     * Get currently pressed movement direction
     */
    getPressedDirection() {
        if (this.isKeyPressed(KEY_CODES.W) || this.isKeyPressed(KEY_CODES.ARROW_UP)) {
            return DIRECTIONS.UP;
        }
        if (this.isKeyPressed(KEY_CODES.S) || this.isKeyPressed(KEY_CODES.ARROW_DOWN)) {
            return DIRECTIONS.DOWN;
        }
        if (this.isKeyPressed(KEY_CODES.A) || this.isKeyPressed(KEY_CODES.ARROW_LEFT)) {
            return DIRECTIONS.LEFT;
        }
        if (this.isKeyPressed(KEY_CODES.D) || this.isKeyPressed(KEY_CODES.ARROW_RIGHT)) {
            return DIRECTIONS.RIGHT;
        }
        return null;
    }

    /**
     * Enable input handling
     */
    enable() {
        this.isEnabled = true;
    }

    /**
     * Disable input handling
     */
    disable() {
        this.isEnabled = false;
        this.keysPressed.clear();
    }

    /**
     * Add custom key binding
     */
    addKeyBinding(keyCode, action, direction = null) {
        this.keyBindings.set(keyCode, { action, direction });
    }

    /**
     * Remove key binding
     */
    removeKeyBinding(keyCode) {
        this.keyBindings.delete(keyCode);
    }

    /**
     * Get all current key bindings
     */
    getKeyBindings() {
        return new Map(this.keyBindings);
    }

    /**
     * Reset to default key bindings
     */
    resetKeyBindings() {
        this.keyBindings.clear();
        this.setupDefaultBindings();
    }

    /**
     * Handle chat input (when chat is focused)
     */
    handleChatInput(inputElement) {
        // Disable game input when typing in chat
        const handleFocus = () => this.disable();
        const handleBlur = () => this.enable();
        const handleKeyDown = (event) => {
            // Stop propagation to prevent game controls
            event.stopPropagation();

            if (event.originalEvent.keyCode === KEY_CODES.ENTER) {
                const message = inputElement.value.trim();
                if (message) {
                    webSocketClient.sendChatMessage(message);
                    inputElement.value = '';
                }
                inputElement.blur();
            } else if (event.originalEvent.keyCode === KEY_CODES.ESCAPE) {
                inputElement.blur();
            }
        };

        const focusUnbind = domEvents.bind(inputElement, 'focus', handleFocus);
        const blurUnbind = domEvents.bind(inputElement, 'blur', handleBlur);
        const keydownUnbind = domEvents.bind(inputElement, 'keydown', handleKeyDown);

        // Return cleanup function
        return () => {
            focusUnbind();
            blurUnbind();
            keydownUnbind();
        };
    }

    /**
     * Update input manager (called each frame)
     */
    update(deltaTime) {
        // Handle continuous movement if key is held down
        if (this.isEnabled && gameState.isGameInProgress()) {
            const direction = this.getPressedDirection();
            if (direction) {
                const now = Date.now();
                if (now - this.lastMoveTime >= this.moveThrottle) {
                    this.handleMovement(direction);
                }
            }
        }
    }

    /**
     * Cleanup event listeners
     */
    destroy() {
        this.disable();
        // The framework's domEvents will handle cleanup automatically
    }
}

// Create singleton instance
export const inputManager = new InputManager();
