/**
 * WebSocket Client for Multiplayer Communication
 * Handles real-time communication with the game server
 */

import { eventEmitter } from '../../mini-framework/src/framework.js';
import { MESSAGE_TYPES, GAME_CONFIG } from '../config/constants.js';
import { gameState } from '../core/GameState.js';

export class WebSocketClient {
    constructor() {
        this.ws = null;
        this.url = this.getWebSocketUrl();
        this.isConnected = false;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = GAME_CONFIG.MAX_RECONNECT_ATTEMPTS;
        this.reconnectDelay = GAME_CONFIG.WEBSOCKET_RECONNECT_DELAY;
        this.messageQueue = [];
        this.heartbeatInterval = null;
        this.connectionTimeout = null;
    }

    /**
     * Get WebSocket URL based on current location
     */
    getWebSocketUrl() {
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const host = window.location.host || 'localhost:3000';
        return `${protocol}//${host}`;
    }

    /**
     * Connect to the WebSocket server
     */
    async connect() {
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            console.log('WebSocket already connected');
            return Promise.resolve();
        }

        return new Promise((resolve, reject) => {
            try {
                console.log('Connecting to WebSocket server:', this.url);
                this.ws = new WebSocket(this.url);

                // Set connection timeout
                this.connectionTimeout = setTimeout(() => {
                    if (this.ws.readyState !== WebSocket.OPEN) {
                        this.ws.close();
                        reject(new Error('Connection timeout'));
                    }
                }, 10000); // 10 second timeout

                this.ws.onopen = () => {
                    console.log('WebSocket connected');
                    clearTimeout(this.connectionTimeout);
                    this.isConnected = true;
                    this.reconnectAttempts = 0;
                    gameState.setConnected(true);
                    gameState.setConnectionError(null);
                    
                    // Send queued messages
                    this.flushMessageQueue();
                    
                    // Start heartbeat
                    this.startHeartbeat();
                    
                    // Emit connection event
                    eventEmitter.emit('websocket:connected');
                    resolve();
                };

                this.ws.onmessage = (event) => {
                    this.handleMessage(event.data);
                };

                this.ws.onclose = (event) => {
                    console.log('WebSocket disconnected:', event.code, event.reason);
                    this.handleDisconnection();
                };

                this.ws.onerror = (error) => {
                    console.error('WebSocket error:', error);
                    clearTimeout(this.connectionTimeout);
                    gameState.setConnectionError('Connection failed');
                    reject(error);
                };

            } catch (error) {
                console.error('Failed to create WebSocket connection:', error);
                reject(error);
            }
        });
    }

    /**
     * Handle incoming messages
     */
    handleMessage(data) {
        try {
            const message = JSON.parse(data);

            // Don't log ping/pong messages to reduce console spam
            if (message.type !== 'pong' && message.type !== 'ping') {
                console.log('Received message:', message.type, message);
            }

            switch (message.type) {
                case MESSAGE_TYPES.JOIN_SUCCESS:
                    this.handleJoinSuccess(message);
                    break;
                case MESSAGE_TYPES.PLAYERS_UPDATE:
                    this.handlePlayersUpdate(message);
                    break;
                case MESSAGE_TYPES.COUNTDOWN_START:
                    this.handleCountdownStart(message);
                    break;
                case MESSAGE_TYPES.GAME_START:
                    this.handleGameStart(message);
                    break;
                case MESSAGE_TYPES.GAME_END:
                    this.handleGameEnd(message);
                    break;
                case MESSAGE_TYPES.GAME_RESTART:
                    this.handleGameRestart(message);
                    break;
                case MESSAGE_TYPES.PLAYER_LEFT:
                    this.handlePlayerLeft(message);
                    break;
                case MESSAGE_TYPES.PLAYER_POSITION:
                    this.handlePlayerPosition(message);
                    break;
                case MESSAGE_TYPES.BOMB_PLACED:
                    this.handleBombPlaced(message);
                    break;
                case MESSAGE_TYPES.EXPLOSION:
                    this.handleExplosion(message);
                    break;
                case MESSAGE_TYPES.EXPLOSION_END:
                    this.handleExplosionEnd(message);
                    break;
                case MESSAGE_TYPES.CHAT_MESSAGE:
                    this.handleChatMessage(message);
                    break;
                case MESSAGE_TYPES.CHAT_HISTORY:
                    this.handleChatHistory(message);
                    break;
                case MESSAGE_TYPES.ERROR:
                    this.handleError(message);
                    break;
                case 'pong':
                    // Heartbeat response - connection is alive
                    break;
                default:
                    console.warn('Unknown message type:', message.type);
            }

            // Emit generic message event
            eventEmitter.emit('websocket:message', message);

        } catch (error) {
            console.error('Error parsing WebSocket message:', error);
        }
    }

    /**
     * Handle successful join
     */
    handleJoinSuccess(message) {
        gameState.setPlayerId(message.playerId);
        gameState.setRoomId(message.roomId);
        gameState.setGameState(message.gameState);
        eventEmitter.emit('game:joined', message);
    }

    /**
     * Handle players update
     */
    handlePlayersUpdate(message) {
        gameState.setPlayers(message.players);
        gameState.setGameState(message.gameState);
        eventEmitter.emit('game:players-updated', message);
    }

    /**
     * Handle countdown start
     */
    handleCountdownStart(message) {
        gameState.setGameState('countdown');
        eventEmitter.emit('game:countdown-start', message);
    }

    /**
     * Handle game start
     */
    handleGameStart(message) {
        gameState.setGameState('playing');
        gameState.setMap(message.gameData.map);
        gameState.setPlayers(message.players);
        eventEmitter.emit('game:started', message);
    }

    /**
     * Handle game end
     */
    handleGameEnd(message) {
        gameState.setGameState('finished');
        gameState.setWinner(message.winner);
        eventEmitter.emit('game:ended', message);
    }

    /**
     * Handle game restart
     */
    handleGameRestart(message) {
        gameState.resetGameState();
        gameState.setPlayers(message.players);
        eventEmitter.emit('game:restarted', message);
    }

    /**
     * Handle player left
     */
    handlePlayerLeft(message) {
        gameState.removePlayer(message.playerId);
        eventEmitter.emit('game:player-left', message);
    }

    /**
     * Handle player position update
     */
    handlePlayerPosition(message) {
        gameState.updatePlayer(message.playerId, {
            x: message.x,
            y: message.y
        });
        eventEmitter.emit('game:player-moved', message);
    }

    /**
     * Handle bomb placement
     */
    handleBombPlaced(message) {
        gameState.addBomb(message.bomb);
        eventEmitter.emit('game:bomb-placed', message);
    }

    /**
     * Handle explosion
     */
    handleExplosion(message) {
        gameState.removeBomb(message.bombId);
        gameState.setExplosions(message.explosions);
        gameState.setMap(message.mapUpdate);
        gameState.setPowerups(message.powerups);
        gameState.setPlayers(message.players);
        eventEmitter.emit('game:explosion', message);
    }

    /**
     * Handle explosion end
     */
    handleExplosionEnd(message) {
        gameState.setExplosions([]);
        eventEmitter.emit('game:explosion-end', message);
    }

    /**
     * Handle chat message
     */
    handleChatMessage(message) {
        gameState.addChatMessage(message.message);
        eventEmitter.emit('chat:message', message);
    }

    /**
     * Handle chat history
     */
    handleChatHistory(message) {
        gameState.setChatHistory(message.messages);
        eventEmitter.emit('chat:history', message);
    }

    /**
     * Handle error message
     */
    handleError(message) {
        console.error('Server error:', message.message);
        gameState.setConnectionError(message.message);
        eventEmitter.emit('websocket:error', message);
    }

    /**
     * Handle disconnection
     */
    handleDisconnection() {
        this.isConnected = false;
        gameState.setConnected(false);
        this.stopHeartbeat();
        
        eventEmitter.emit('websocket:disconnected');
        
        // Attempt to reconnect
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.scheduleReconnect();
        } else {
            gameState.setConnectionError('Connection lost. Please refresh the page.');
        }
    }

    /**
     * Schedule reconnection attempt
     */
    scheduleReconnect() {
        this.reconnectAttempts++;
        const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1); // Exponential backoff
        
        console.log(`Reconnecting in ${delay}ms (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
        
        setTimeout(() => {
            if (!this.isConnected) {
                this.connect().catch(error => {
                    console.error('Reconnection failed:', error);
                });
            }
        }, delay);
    }

    /**
     * Send message to server
     */
    send(message) {
        if (this.isConnected && this.ws.readyState === WebSocket.OPEN) {
            try {
                this.ws.send(JSON.stringify(message));
                return true;
            } catch (error) {
                console.error('Failed to send message:', error);
                this.messageQueue.push(message);
                return false;
            }
        } else {
            // Queue message for later
            this.messageQueue.push(message);
            return false;
        }
    }

    /**
     * Send queued messages
     */
    flushMessageQueue() {
        while (this.messageQueue.length > 0 && this.isConnected) {
            const message = this.messageQueue.shift();
            this.send(message);
        }
    }

    /**
     * Start heartbeat to keep connection alive
     */
    startHeartbeat() {
        this.heartbeatInterval = setInterval(() => {
            if (this.isConnected) {
                this.send({ type: 'ping' });
            }
        }, 30000); // 30 seconds
    }

    /**
     * Stop heartbeat
     */
    stopHeartbeat() {
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
            this.heartbeatInterval = null;
        }
    }

    /**
     * Join game with nickname
     */
    joinGame(nickname) {
        return this.send({
            type: MESSAGE_TYPES.JOIN_GAME,
            nickname: nickname
        });
    }

    /**
     * Send chat message
     */
    sendChatMessage(message) {
        return this.send({
            type: MESSAGE_TYPES.CHAT_MESSAGE,
            message: message
        });
    }

    /**
     * Send player movement
     */
    sendPlayerMove(x, y) {
        return this.send({
            type: MESSAGE_TYPES.PLAYER_MOVE,
            x: x,
            y: y
        });
    }

    /**
     * Send bomb placement
     */
    sendPlaceBomb() {
        return this.send({
            type: MESSAGE_TYPES.PLACE_BOMB
        });
    }

    /**
     * Send restart game request
     */
    sendRestartGame() {
        return this.send({
            type: MESSAGE_TYPES.RESTART_GAME
        });
    }

    /**
     * Disconnect from server
     */
    disconnect() {
        this.stopHeartbeat();
        if (this.ws) {
            this.ws.close();
        }
        this.isConnected = false;
        gameState.setConnected(false);
    }

    /**
     * Get connection status
     */
    getConnectionStatus() {
        return {
            isConnected: this.isConnected,
            reconnectAttempts: this.reconnectAttempts,
            queuedMessages: this.messageQueue.length
        };
    }
}

// Create singleton instance
export const webSocketClient = new WebSocketClient();
