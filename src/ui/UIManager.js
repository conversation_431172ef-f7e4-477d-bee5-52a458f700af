/**
 * UI Manager
 * Manages all user interface components and screens
 */

import { globalState, eventEmitter } from '../../mini-framework/src/framework.js';
import { GAME_STATES } from '../config/constants.js';
import { gameState } from '../core/GameState.js';
import { webSocketClient } from '../network/WebSocketClient.js';
import { inputManager } from '../input/InputManager.js';
import { sanitizeHtml } from '../utils/helpers.js';

export class UIManager {
    constructor() {
        this.screens = new Map();
        this.currentScreen = null;
        this.chatCleanup = null;
        this.countdownInterval = null;
        this.performanceUpdateInterval = null;
    }

    /**
     * Initialize UI manager
     */
    initialize() {
        this.setupScreens();
        this.setupChatSystem();
        this.setupPerformanceMonitor();
        this.setupEventListeners();
        
        console.log('UI Manager initialized');
    }

    /**
     * Setup screen references
     */
    setupScreens() {
        this.screens.set('loading', document.getElementById('loading-screen'));
        this.screens.set('lobby', document.getElementById('lobby-screen'));
        this.screens.set('game', document.getElementById('game-screen'));
        this.screens.set('gameOver', document.getElementById('game-over-screen'));
    }

    /**
     * Setup chat system
     */
    setupChatSystem() {
        const chatInput = document.getElementById('chat-input');
        const chatMessages = document.getElementById('chat-messages');

        if (chatInput) {
            this.chatCleanup = inputManager.handleChatInput(chatInput);
        }

        // Subscribe to chat messages
        globalState.subscribe('chatMessages', (messages) => {
            this.updateChatDisplay(messages);
        });
    }

    /**
     * Setup performance monitor
     */
    setupPerformanceMonitor() {
        const performanceMonitor = document.getElementById('performance-monitor');
        
        if (performanceMonitor) {
            // Update performance display every second
            this.performanceUpdateInterval = setInterval(() => {
                this.updatePerformanceDisplay();
            }, 1000);
        }
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Restart button
        const restartButton = document.getElementById('restart-button');
        if (restartButton) {
            restartButton.addEventListener('click', () => {
                webSocketClient.sendRestartGame();
            });
        }

        // Game state changes
        eventEmitter.on('game:countdown-start', (data) => {
            this.startCountdown(data.duration);
        });

        eventEmitter.on('game:players-updated', (data) => {
            this.updatePlayersList(data.players);
            this.updatePlayersInfo(data.players);
        });
    }

    /**
     * Show loading screen
     */
    showLoadingScreen() {
        this.showScreen('loading');
    }

    /**
     * Show lobby screen
     */
    showLobbyScreen() {
        this.showScreen('lobby');
        this.updateLobbyInfo();
    }

    /**
     * Show countdown screen
     */
    showCountdownScreen() {
        // Countdown is shown over lobby
        this.showScreen('lobby');
    }

    /**
     * Show game screen
     */
    showGameScreen() {
        this.showScreen('game');
        this.updateGameUI();
    }

    /**
     * Show game over screen
     */
    showGameOverScreen() {
        this.showScreen('gameOver');
        this.updateGameOverDisplay();
    }

    /**
     * Show a specific screen
     */
    showScreen(screenName) {
        // Hide all screens
        for (const [name, screen] of this.screens) {
            if (screen) {
                screen.classList.add('hidden');
            }
        }

        // Show target screen
        const targetScreen = this.screens.get(screenName);
        if (targetScreen) {
            targetScreen.classList.remove('hidden');
            this.currentScreen = screenName;
        }
    }

    /**
     * Update lobby information
     */
    updateLobbyInfo() {
        const playerCount = gameState.getPlayerCount();
        const lobbyInfo = document.querySelector('.lobby-screen .lobby-info h2');
        
        if (lobbyInfo) {
            if (playerCount < 2) {
                lobbyInfo.textContent = 'Waiting for Players...';
            } else {
                lobbyInfo.textContent = 'Ready to Start!';
            }
        }
    }

    /**
     * Update players list in lobby
     */
    updatePlayersList(players) {
        const playerList = document.getElementById('player-list');
        if (!playerList) return;

        playerList.innerHTML = '';

        if (Array.isArray(players)) {
            players.forEach((player, index) => {
                const playerItem = document.createElement('div');
                playerItem.className = 'player-item';
                playerItem.innerHTML = `
                    <span class="player-name">${sanitizeHtml(player.nickname)}</span>
                    <span class="player-status">Ready</span>
                `;
                playerList.appendChild(playerItem);
            });
        } else {
            // Handle Map of players
            let index = 0;
            for (const player of players.values()) {
                const playerItem = document.createElement('div');
                playerItem.className = 'player-item';
                playerItem.innerHTML = `
                    <span class="player-name">${sanitizeHtml(player.nickname)}</span>
                    <span class="player-status">Ready</span>
                `;
                playerList.appendChild(playerItem);
                index++;
            }
        }
    }

    /**
     * Update players info in game screen
     */
    updatePlayersInfo(players) {
        const playersInfo = document.getElementById('players-info');
        if (!playersInfo) return;

        playersInfo.innerHTML = '';

        const playerArray = Array.isArray(players) ? players : Array.from(players.values());
        
        playerArray.forEach((player, index) => {
            const playerInfo = document.createElement('div');
            playerInfo.className = `player-info ${player.isAlive ? '' : 'dead'}`;
            playerInfo.innerHTML = `
                <span class="player-name" style="color: ${this.getPlayerColor(index)}">${sanitizeHtml(player.nickname)}</span>
                <span class="player-lives">❤️ ${player.lives}</span>
            `;
            playersInfo.appendChild(playerInfo);
        });
    }

    /**
     * Get player color by index
     */
    getPlayerColor(index) {
        const colors = ['#e74c3c', '#3498db', '#2ecc71', '#f39c12'];
        return colors[index % colors.length];
    }

    /**
     * Start countdown display
     */
    startCountdown(duration) {
        const countdownDisplay = document.getElementById('countdown-display');
        if (!countdownDisplay) return;

        countdownDisplay.classList.remove('hidden');
        
        let remaining = Math.ceil(duration / 1000);
        
        const updateCountdown = () => {
            countdownDisplay.textContent = `Game starts in ${remaining}...`;
            remaining--;
            
            if (remaining < 0) {
                countdownDisplay.classList.add('hidden');
                clearInterval(this.countdownInterval);
                this.countdownInterval = null;
            }
        };

        updateCountdown();
        this.countdownInterval = setInterval(updateCountdown, 1000);
    }

    /**
     * Update game UI
     */
    updateGameUI() {
        const players = gameState.getPlayers();
        this.updatePlayersInfo(players);
    }

    /**
     * Update game over display
     */
    updateGameOverDisplay() {
        const winner = gameState.getWinner();
        const winnerDisplay = document.getElementById('winner-display');
        
        if (winnerDisplay) {
            if (winner) {
                winnerDisplay.textContent = `${winner.nickname} Wins!`;
            } else {
                winnerDisplay.textContent = 'Draw!';
            }
        }
    }

    /**
     * Update chat display
     */
    updateChatDisplay(messages) {
        const chatMessages = document.getElementById('chat-messages');
        if (!chatMessages) return;

        chatMessages.innerHTML = '';

        messages.forEach(message => {
            const messageElement = document.createElement('div');
            messageElement.className = 'chat-message';
            messageElement.innerHTML = `
                <strong>${sanitizeHtml(message.nickname)}:</strong> 
                ${sanitizeHtml(message.message)}
            `;
            chatMessages.appendChild(messageElement);
        });

        // Scroll to bottom
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }

    /**
     * Update performance display
     */
    updatePerformanceDisplay() {
        const performance = gameState.getPerformance();
        const playerCount = gameState.getPlayerCount();

        const fpsCounter = document.getElementById('fps-counter');
        const frameTime = document.getElementById('frame-time');
        const playerCountDisplay = document.getElementById('player-count');

        if (fpsCounter) {
            fpsCounter.textContent = performance.fps;
        }

        if (frameTime) {
            frameTime.textContent = `${performance.frameTime}ms`;
        }

        if (playerCountDisplay) {
            playerCountDisplay.textContent = playerCount;
        }
    }

    /**
     * Toggle performance monitor
     */
    togglePerformanceMonitor(show) {
        const performanceMonitor = document.getElementById('performance-monitor');
        if (performanceMonitor) {
            performanceMonitor.classList.toggle('hidden', !show);
        }
    }

    /**
     * Show connection status
     */
    showConnectionStatus(message, type = 'info') {
        const connectionStatus = document.getElementById('connection-status');
        if (connectionStatus) {
            connectionStatus.textContent = message;
            connectionStatus.className = `connection-status ${type}`;
        }
    }

    /**
     * Show error message
     */
    showError(message) {
        // Create or update error display
        let errorDisplay = document.getElementById('error-display');
        
        if (!errorDisplay) {
            errorDisplay = document.createElement('div');
            errorDisplay.id = 'error-display';
            errorDisplay.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #e74c3c;
                color: white;
                padding: 1rem;
                border-radius: 4px;
                z-index: 10000;
                max-width: 300px;
            `;
            document.body.appendChild(errorDisplay);
        }

        errorDisplay.textContent = message;
        errorDisplay.style.display = 'block';

        // Auto-hide after 5 seconds
        setTimeout(() => {
            if (errorDisplay) {
                errorDisplay.style.display = 'none';
            }
        }, 5000);
    }

    /**
     * Update UI (called each frame)
     */
    update(deltaTime) {
        // Update any animated UI elements
        if (this.currentScreen === 'game') {
            this.updateGameUI();
        }
    }

    /**
     * Render UI (called each frame)
     */
    render() {
        // Handle any UI rendering that needs to happen each frame
        // Most UI updates are event-driven, so this might be minimal
    }

    /**
     * Cleanup UI manager
     */
    cleanup() {
        // Clear intervals
        if (this.countdownInterval) {
            clearInterval(this.countdownInterval);
            this.countdownInterval = null;
        }

        if (this.performanceUpdateInterval) {
            clearInterval(this.performanceUpdateInterval);
            this.performanceUpdateInterval = null;
        }

        // Cleanup chat
        if (this.chatCleanup) {
            this.chatCleanup();
            this.chatCleanup = null;
        }

        console.log('UI Manager cleaned up');
    }
}

export default UIManager;
