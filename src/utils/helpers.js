/**
 * Utility Helper Functions
 * Shared utility functions for the game
 */

import { GAME_CONFIG, SAFE_ZONES } from '../config/constants.js';

/**
 * Generate a unique ID
 */
export function generateId(prefix = 'id') {
    return `${prefix}_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
}

/**
 * Clamp a value between min and max
 */
export function clamp(value, min, max) {
    return Math.min(Math.max(value, min), max);
}

/**
 * Check if two positions are equal
 */
export function positionsEqual(pos1, pos2) {
    return pos1.x === pos2.x && pos1.y === pos2.y;
}

/**
 * Calculate distance between two positions
 */
export function distance(pos1, pos2) {
    const dx = pos1.x - pos2.x;
    const dy = pos1.y - pos2.y;
    return Math.sqrt(dx * dx + dy * dy);
}

/**
 * Check if a position is within map bounds
 */
export function isValidPosition(x, y) {
    return x >= 0 && x < GAME_CONFIG.MAP_WIDTH && 
           y >= 0 && y < GAME_CONFIG.MAP_HEIGHT;
}

/**
 * Check if a position is in a safe zone (spawn area)
 */
export function isInSafeZone(x, y) {
    return SAFE_ZONES.some(zone => zone.x === x && zone.y === y);
}

/**
 * Get random element from array
 */
export function randomElement(array) {
    return array[Math.floor(Math.random() * array.length)];
}

/**
 * Shuffle array in place
 */
export function shuffleArray(array) {
    for (let i = array.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [array[i], array[j]] = [array[j], array[i]];
    }
    return array;
}

/**
 * Deep clone an object
 */
export function deepClone(obj) {
    if (obj === null || typeof obj !== 'object') {
        return obj;
    }
    
    if (obj instanceof Date) {
        return new Date(obj.getTime());
    }
    
    if (obj instanceof Array) {
        return obj.map(item => deepClone(item));
    }
    
    if (typeof obj === 'object') {
        const cloned = {};
        for (const key in obj) {
            if (obj.hasOwnProperty(key)) {
                cloned[key] = deepClone(obj[key]);
            }
        }
        return cloned;
    }
}

/**
 * Throttle function execution
 */
export function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

/**
 * Sanitize HTML to prevent XSS
 */
export function sanitizeHtml(str) {
    const div = document.createElement('div');
    div.textContent = str;
    return div.innerHTML;
}

/**
 * Create a 2D array filled with a value
 */
export function create2DArray(width, height, fillValue = null) {
    return Array(height).fill().map(() => Array(width).fill(fillValue));
}

/**
 * Get the current timestamp in milliseconds
 */
export function now() {
    return performance.now ? performance.now() : Date.now();
}

/**
 * Create a promise that resolves when the DOM is ready
 */
export function domReady() {
    return new Promise(resolve => {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', resolve);
        } else {
            resolve();
        }
    });
}


