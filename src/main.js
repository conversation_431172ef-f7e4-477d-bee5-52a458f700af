/**
 * Main Application Entry Point
 * Initializes and coordinates all game systems
 */

import { createApp, eventEmitter, globalState } from '../mini-framework/src/framework.js';
import { GAME_STATES } from './config/constants.js';
import { gameState } from './core/GameState.js';
import { webSocketClient } from './network/WebSocketClient.js';
import { inputManager } from './input/InputManager.js';
import { gameRenderer } from './rendering/GameRenderer.js';
import { GameEngine } from './core/GameEngine.js';
import { UIManager } from './ui/UIManager.js';
import { domReady } from './utils/helpers.js';

class BombermanGame {
    constructor() {
        this.app = null;
        this.gameEngine = null;
        this.uiManager = null;
        this.isInitialized = false;
        this.gameLoop = null;
        this.lastFrameTime = 0;
    }

    /**
     * Initialize the game application
     */
    async initialize() {
        try {
            console.log('Initializing Bomberman DOM Game...');

            // Wait for DOM to be ready
            await domReady();

            // Create mini-framework app
            this.app = createApp({
                name: 'Bomberman DOM',
                version: '1.0.0'
            });

            // Initialize app with root element
            const rootElement = document.getElementById('app');
            if (!rootElement) {
                throw new Error('Root element #app not found');
            }

            this.app.init(rootElement);

            // Initialize game systems
            await this.initializeGameSystems();

            // Setup event listeners
            this.setupEventListeners();

            // Initialize UI
            this.initializeUI();

            // Start the game loop
            this.startGameLoop();

            this.isInitialized = true;
            console.log('Game initialized successfully');

        } catch (error) {
            console.error('Failed to initialize game:', error);
            this.showError('Failed to initialize game: ' + error.message);
        }
    }

    /**
     * Initialize core game systems
     */
    async initializeGameSystems() {
        // Initialize game engine
        this.gameEngine = new GameEngine();

        // Initialize UI manager
        this.uiManager = new UIManager();

        // Initialize renderer
        const gameBoard = document.getElementById('game-board');
        if (gameBoard) {
            gameRenderer.initialize(gameBoard);
        }

        // Initialize input manager (already done in constructor)
        console.log('Game systems initialized');
    }

    /**
     * Setup global event listeners
     */
    setupEventListeners() {
        // Game state changes
        globalState.subscribe('gameState', (newState) => {
            this.handleGameStateChange(newState);
        });

        // Network events
        eventEmitter.on('websocket:connected', () => {
            console.log('Connected to server');
            this.uiManager.showConnectionStatus('Connected', 'success');
        });

        eventEmitter.on('websocket:disconnected', () => {
            console.log('Disconnected from server');
            this.uiManager.showConnectionStatus('Disconnected', 'error');
        });

        eventEmitter.on('websocket:error', (error) => {
            console.error('WebSocket error:', error);
            this.uiManager.showConnectionStatus('Connection Error', 'error');
        });

        // Game events
        eventEmitter.on('game:joined', (data) => {
            console.log('Joined game:', data);
            gameState.setGameState(GAME_STATES.LOBBY);
        });

        eventEmitter.on('game:started', (data) => {
            console.log('Game started:', data);
            this.gameEngine.startGame(data);
        });

        eventEmitter.on('game:ended', (data) => {
            console.log('Game ended:', data);
            this.gameEngine.endGame(data);
        });

        // Performance monitoring
        globalState.subscribe('showPerformanceMonitor', (show) => {
            this.uiManager.togglePerformanceMonitor(show);
        });

        // Window events
        window.addEventListener('beforeunload', () => {
            this.cleanup();
        });

        window.addEventListener('resize', () => {
            gameRenderer.resize();
        });

        // Visibility change (pause/resume)
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.pauseGame();
            } else {
                this.resumeGame();
            }
        });
    }

    /**
     * Handle game state changes
     */
    handleGameStateChange(newState) {
        console.log('Game state changed to:', newState);
        
        switch (newState) {
            case GAME_STATES.LOADING:
                this.uiManager.showLoadingScreen();
                break;
            case GAME_STATES.LOBBY:
                this.uiManager.showLobbyScreen();
                break;
            case GAME_STATES.COUNTDOWN:
                this.uiManager.showCountdownScreen();
                break;
            case GAME_STATES.PLAYING:
                this.uiManager.showGameScreen();
                inputManager.enable();
                break;
            case GAME_STATES.FINISHED:
                this.uiManager.showGameOverScreen();
                inputManager.disable();
                break;
        }
    }

    /**
     * Initialize UI components
     */
    initializeUI() {
        this.uiManager.initialize();
        
        // Setup initial screen
        gameState.setGameState(GAME_STATES.LOADING);
        
        // Setup connection UI
        this.setupConnectionUI();
    }

    /**
     * Setup connection UI handlers
     */
    setupConnectionUI() {
        const nicknameInput = document.getElementById('nickname-input');
        const joinButton = document.getElementById('join-button');
        const connectionStatus = document.getElementById('connection-status');

        if (joinButton) {
            joinButton.addEventListener('click', async () => {
                const nickname = nicknameInput?.value?.trim();
                if (!nickname) {
                    this.uiManager.showError('Please enter a nickname');
                    return;
                }

                try {
                    joinButton.disabled = true;
                    joinButton.textContent = 'Connecting...';
                    
                    // Connect to server
                    await webSocketClient.connect();
                    
                    // Join game
                    gameState.setNickname(nickname);
                    webSocketClient.joinGame(nickname);
                    
                } catch (error) {
                    console.error('Failed to connect:', error);
                    this.uiManager.showError('Failed to connect to server');
                    joinButton.disabled = false;
                    joinButton.textContent = 'Join Game';
                }
            });
        }

        if (nicknameInput) {
            nicknameInput.addEventListener('keydown', (event) => {
                if (event.key === 'Enter') {
                    joinButton?.click();
                }
            });
        }
    }

    /**
     * Start the main game loop
     */
    startGameLoop() {
        const gameLoop = (currentTime) => {
            const deltaTime = currentTime - this.lastFrameTime;
            this.lastFrameTime = currentTime;

            // Update game systems
            this.update(deltaTime);

            // Render game
            this.render();

            // Continue loop
            this.gameLoop = requestAnimationFrame(gameLoop);
        };

        this.gameLoop = requestAnimationFrame(gameLoop);
        console.log('Game loop started');
    }

    /**
     * Update game systems
     */
    update(deltaTime) {
        // Update input manager
        inputManager.update(deltaTime);

        // Update game engine
        if (this.gameEngine) {
            this.gameEngine.update(deltaTime);
        }

        // Update UI
        if (this.uiManager) {
            this.uiManager.update(deltaTime);
        }
    }

    /**
     * Render the game
     */
    render() {
        // Only render if game is in playing state
        if (gameState.getGameState() === GAME_STATES.PLAYING) {
            gameRenderer.render();
        }

        // Update UI
        if (this.uiManager) {
            this.uiManager.render();
        }
    }

    /**
     * Pause the game
     */
    pauseGame() {
        if (this.gameLoop) {
            cancelAnimationFrame(this.gameLoop);
            this.gameLoop = null;
        }
        inputManager.disable();
    }

    /**
     * Resume the game
     */
    resumeGame() {
        if (!this.gameLoop) {
            this.startGameLoop();
        }
        inputManager.enable();
    }

    /**
     * Show error message
     */
    showError(message) {
        console.error(message);
        if (this.uiManager) {
            this.uiManager.showError(message);
        } else {
            alert(message);
        }
    }

    /**
     * Cleanup resources
     */
    cleanup() {
        console.log('Cleaning up game resources...');

        // Stop game loop
        if (this.gameLoop) {
            cancelAnimationFrame(this.gameLoop);
            this.gameLoop = null;
        }

        // Disconnect from server
        if (webSocketClient) {
            webSocketClient.disconnect();
        }

        // Cleanup input manager
        if (inputManager) {
            inputManager.destroy();
        }

        // Cleanup renderer
        if (gameRenderer) {
            gameRenderer.clear();
        }

        // Cleanup UI
        if (this.uiManager) {
            this.uiManager.cleanup();
        }

        this.isInitialized = false;
    }

    /**
     * Get game status
     */
    getStatus() {
        return {
            initialized: this.isInitialized,
            gameState: gameState.getGameState(),
            connected: gameState.get('connected'),
            playerCount: gameState.getPlayerCount(),
            performance: gameState.getPerformance()
        };
    }
}

// Create and initialize the game
const game = new BombermanGame();

// Initialize when DOM is ready
domReady().then(() => {
    game.initialize().catch(error => {
        console.error('Failed to start game:', error);
    });
});

// Export for debugging
window.BombermanGame = game;
window.gameState = gameState;
window.webSocketClient = webSocketClient;

export default game;
