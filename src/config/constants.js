/**
 * Game Constants and Configuration
 * Shared between client and server
 */

export const GAME_CONFIG = {
    // Player settings
    MAX_PLAYERS: 4,
    MIN_PLAYERS: 1,
    INITIAL_LIVES: 3,
    INITIAL_BOMB_CAPACITY: 1,
    INITIAL_BOMB_RANGE: 1,
    INITIAL_SPEED: 1,

    // Map settings
    MAP_WIDTH: 15,
    MAP_HEIGHT: 13,
    CELL_SIZE: 32,

    // Timing settings
    LOBBY_TIMEOUT: 20000,     // 20 seconds
    COUNTDOWN_TIME: 10000,    // 10 seconds
    BOMB_TIMER: 3000,         // 3 seconds
    EXPLOSION_DURATION: 500,  // 0.5 seconds

    // Game mechanics
    BLOCK_SPAWN_CHANCE: 0.6,
    POWERUP_SPAWN_CHANCE: 0.3,
    
    // Performance settings
    TARGET_FPS: 60,
    PERFORMANCE_SAMPLE_SIZE: 60,

    // Network settings
    WEBSOCKET_RECONNECT_DELAY: 3000,
    MAX_RECONNECT_ATTEMPTS: 5
};

export const CELL_TYPES = {
    EMPTY: 'empty',
    WALL: 'wall',
    BLOCK: 'block'
};

export const POWERUP_TYPES = {
    BOMB: 'bomb',      // +1 bomb capacity
    FLAME: 'flame',    // +1 explosion range
    SPEED: 'speed'     // +1 movement speed
};

export const GAME_STATES = {
    LOADING: 'loading',
    LOBBY: 'lobby',
    COUNTDOWN: 'countdown',
    PLAYING: 'playing',
    FINISHED: 'finished'
};

export const PLAYER_COLORS = [
    '#e74c3c', // Red
    '#3498db', // Blue
    '#2ecc71', // Green
    '#f39c12'  // Orange
];

export const SPAWN_POSITIONS = [
    { x: 1, y: 1 },     // Top-left
    { x: 13, y: 1 },    // Top-right
    { x: 1, y: 11 },    // Bottom-left
    { x: 13, y: 11 }    // Bottom-right
];

export const SAFE_ZONES = [
    // Top-left safe zone
    { x: 1, y: 1 }, { x: 2, y: 1 }, { x: 1, y: 2 },
    // Top-right safe zone
    { x: 13, y: 1 }, { x: 12, y: 1 }, { x: 13, y: 2 },
    // Bottom-left safe zone
    { x: 1, y: 11 }, { x: 2, y: 11 }, { x: 1, y: 10 },
    // Bottom-right safe zone
    { x: 13, y: 11 }, { x: 12, y: 11 }, { x: 13, y: 10 }
];

export const DIRECTIONS = {
    UP: { x: 0, y: -1 },
    DOWN: { x: 0, y: 1 },
    LEFT: { x: -1, y: 0 },
    RIGHT: { x: 1, y: 0 }
};

export const KEY_CODES = {
    ENTER: 13,
    ESCAPE: 27,
    SPACE: 32,
    ARROW_LEFT: 37,
    ARROW_UP: 38,
    ARROW_RIGHT: 39,
    ARROW_DOWN: 40,
    W: 87,
    A: 65,
    S: 83,
    D: 68
};

export const MESSAGE_TYPES = {
    // Client to Server
    JOIN_GAME: 'join_game',
    CHAT_MESSAGE: 'chat_message',
    PLAYER_MOVE: 'player_move',
    PLACE_BOMB: 'place_bomb',
    RESTART_GAME: 'restart_game',

    // Server to Client
    JOIN_SUCCESS: 'join_success',
    PLAYERS_UPDATE: 'players_update',
    COUNTDOWN_START: 'countdown_start',
    GAME_START: 'game_start',
    GAME_END: 'game_end',
    GAME_RESTART: 'game_restart',
    PLAYER_LEFT: 'player_left',
    PLAYER_POSITION: 'player_position',
    BOMB_PLACED: 'bomb_placed',
    EXPLOSION: 'explosion',
    EXPLOSION_END: 'explosion_end',
    CHAT_HISTORY: 'chat_history',
    ERROR: 'error'
};
