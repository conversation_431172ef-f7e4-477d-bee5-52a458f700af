/**
 * Map Generation and Management
 * Handles game map creation, updates, and collision detection
 */

import { GAME_CONFIG, CELL_TYPES, SAFE_ZONES } from '../config/constants.js';
import { create2DArray, isInSafeZone } from '../utils/helpers.js';

export class GameMap {
    constructor() {
        this.width = GAME_CONFIG.MAP_WIDTH;
        this.height = GAME_CONFIG.MAP_HEIGHT;
        this.cells = create2DArray(this.width, this.height, CELL_TYPES.EMPTY);
        this.originalMap = null; // Store original map for resets
    }

    /**
     * Generate a new map with walls and blocks
     */
    generate() {
        // Initialize empty map
        this.cells = create2DArray(this.width, this.height, CELL_TYPES.EMPTY);

        // Add border walls
        this.addBorderWalls();

        // Add internal walls (grid pattern)
        this.addInternalWalls();

        // Add destructible blocks
        this.addDestructibleBlocks();

        // Store original map for resets
        this.originalMap = this.cells.map(row => [...row]);

        return this.cells;
    }

    /**
     * Add walls around the border
     */
    addBorderWalls() {
        for (let y = 0; y < this.height; y++) {
            for (let x = 0; x < this.width; x++) {
                if (x === 0 || x === this.width - 1 || 
                    y === 0 || y === this.height - 1) {
                    this.cells[y][x] = CELL_TYPES.WALL;
                }
            }
        }
    }

    /**
     * Add internal walls in a grid pattern
     */
    addInternalWalls() {
        for (let y = 2; y < this.height - 1; y += 2) {
            for (let x = 2; x < this.width - 1; x += 2) {
                this.cells[y][x] = CELL_TYPES.WALL;
            }
        }
    }

    /**
     * Add destructible blocks randomly, avoiding safe zones
     */
    addDestructibleBlocks() {
        for (let y = 1; y < this.height - 1; y++) {
            for (let x = 1; x < this.width - 1; x++) {
                // Skip if already occupied
                if (this.cells[y][x] !== CELL_TYPES.EMPTY) {
                    continue;
                }

                // Skip safe zones around spawn points
                if (isInSafeZone(x, y)) {
                    continue;
                }

                // Randomly place blocks
                if (Math.random() < GAME_CONFIG.BLOCK_SPAWN_CHANCE) {
                    this.cells[y][x] = CELL_TYPES.BLOCK;
                }
            }
        }
    }

    /**
     * Get cell type at position
     */
    getCellType(x, y) {
        if (!this.isValidPosition(x, y)) {
            return CELL_TYPES.WALL; // Treat out-of-bounds as walls
        }
        return this.cells[y][x];
    }

    /**
     * Set cell type at position
     */
    setCellType(x, y, cellType) {
        if (this.isValidPosition(x, y)) {
            this.cells[y][x] = cellType;
        }
    }

    /**
     * Check if position is within map bounds
     */
    isValidPosition(x, y) {
        return x >= 0 && x < this.width && y >= 0 && y < this.height;
    }

    /**
     * Check if position is walkable (not wall or block)
     */
    isWalkable(x, y) {
        const cellType = this.getCellType(x, y);
        return cellType === CELL_TYPES.EMPTY;
    }

    /**
     * Check if position blocks explosions (walls)
     */
    blocksExplosion(x, y) {
        const cellType = this.getCellType(x, y);
        return cellType === CELL_TYPES.WALL;
    }

    /**
     * Check if position is destructible (blocks)
     */
    isDestructible(x, y) {
        const cellType = this.getCellType(x, y);
        return cellType === CELL_TYPES.BLOCK;
    }

    /**
     * Destroy a block at position
     */
    destroyBlock(x, y) {
        if (this.isDestructible(x, y)) {
            this.setCellType(x, y, CELL_TYPES.EMPTY);
            return true;
        }
        return false;
    }

    /**
     * Get all empty positions
     */
    getEmptyPositions() {
        const positions = [];
        for (let y = 0; y < this.height; y++) {
            for (let x = 0; x < this.width; x++) {
                if (this.getCellType(x, y) === CELL_TYPES.EMPTY) {
                    positions.push({ x, y });
                }
            }
        }
        return positions;
    }

    /**
     * Get all block positions
     */
    getBlockPositions() {
        const positions = [];
        for (let y = 0; y < this.height; y++) {
            for (let x = 0; x < this.width; x++) {
                if (this.getCellType(x, y) === CELL_TYPES.BLOCK) {
                    positions.push({ x, y });
                }
            }
        }
        return positions;
    }

    /**
     * Find path between two points (simple pathfinding)
     */
    findPath(startX, startY, endX, endY) {
        // Simple BFS pathfinding
        const queue = [{ x: startX, y: startY, path: [] }];
        const visited = new Set();
        const directions = [
            { x: 0, y: -1 }, // Up
            { x: 0, y: 1 },  // Down
            { x: -1, y: 0 }, // Left
            { x: 1, y: 0 }   // Right
        ];

        while (queue.length > 0) {
            const current = queue.shift();
            const key = `${current.x},${current.y}`;

            if (visited.has(key)) continue;
            visited.add(key);

            if (current.x === endX && current.y === endY) {
                return current.path;
            }

            for (const dir of directions) {
                const newX = current.x + dir.x;
                const newY = current.y + dir.y;
                const newKey = `${newX},${newY}`;

                if (!visited.has(newKey) && this.isWalkable(newX, newY)) {
                    queue.push({
                        x: newX,
                        y: newY,
                        path: [...current.path, { x: newX, y: newY }]
                    });
                }
            }
        }

        return null; // No path found
    }

    /**
     * Get neighbors of a position
     */
    getNeighbors(x, y, includeDiagonal = false) {
        const neighbors = [];
        const directions = includeDiagonal ? [
            { x: -1, y: -1 }, { x: 0, y: -1 }, { x: 1, y: -1 },
            { x: -1, y: 0 },                   { x: 1, y: 0 },
            { x: -1, y: 1 },  { x: 0, y: 1 },  { x: 1, y: 1 }
        ] : [
            { x: 0, y: -1 }, // Up
            { x: 0, y: 1 },  // Down
            { x: -1, y: 0 }, // Left
            { x: 1, y: 0 }   // Right
        ];

        for (const dir of directions) {
            const newX = x + dir.x;
            const newY = y + dir.y;
            if (this.isValidPosition(newX, newY)) {
                neighbors.push({
                    x: newX,
                    y: newY,
                    cellType: this.getCellType(newX, newY)
                });
            }
        }

        return neighbors;
    }

    /**
     * Calculate explosion pattern from a bomb position
     */
    calculateExplosionPattern(bombX, bombY, range) {
        const explosions = [];
        const directions = [
            { x: 0, y: 0 },   // Center
            { x: 1, y: 0 },   // Right
            { x: -1, y: 0 },  // Left
            { x: 0, y: 1 },   // Down
            { x: 0, y: -1 }   // Up
        ];

        for (const dir of directions) {
            const maxDistance = dir.x === 0 && dir.y === 0 ? 1 : range;
            
            for (let i = 0; i < maxDistance; i++) {
                const x = bombX + dir.x * i;
                const y = bombY + dir.y * i;

                if (!this.isValidPosition(x, y)) {
                    break;
                }

                explosions.push({ x, y });

                // Stop if we hit a wall
                if (this.blocksExplosion(x, y)) {
                    break;
                }

                // Stop after destroying a block
                if (this.isDestructible(x, y)) {
                    break;
                }
            }
        }

        return explosions;
    }

    /**
     * Apply explosion damage to map
     */
    applyExplosion(explosions) {
        const destroyedBlocks = [];
        
        for (const explosion of explosions) {
            if (this.isDestructible(explosion.x, explosion.y)) {
                this.destroyBlock(explosion.x, explosion.y);
                destroyedBlocks.push({ x: explosion.x, y: explosion.y });
            }
        }

        return destroyedBlocks;
    }

    /**
     * Reset map to original state
     */
    reset() {
        if (this.originalMap) {
            this.cells = this.originalMap.map(row => [...row]);
        } else {
            this.generate();
        }
    }

    /**
     * Get map data for serialization
     */
    serialize() {
        return {
            width: this.width,
            height: this.height,
            cells: this.cells.map(row => [...row])
        };
    }

    /**
     * Load map from serialized data
     */
    deserialize(data) {
        this.width = data.width;
        this.height = data.height;
        this.cells = data.cells.map(row => [...row]);
    }

    /**
     * Get a copy of the current map
     */
    getCopy() {
        return this.cells.map(row => [...row]);
    }

    /**
     * Count remaining destructible blocks
     */
    countBlocks() {
        let count = 0;
        for (let y = 0; y < this.height; y++) {
            for (let x = 0; x < this.width; x++) {
                if (this.getCellType(x, y) === CELL_TYPES.BLOCK) {
                    count++;
                }
            }
        }
        return count;
    }
}
