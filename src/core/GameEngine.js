/**
 * Game Engine
 * Core game logic and coordination
 */

import { eventEmitter } from '../../mini-framework/src/framework.js';
import { GAME_CONFIG, GAME_STATES } from '../config/constants.js';
import { gameState } from './GameState.js';
import { GameMap } from './Map.js';
import { Player } from './Player.js';
import { Bomb, Explosion } from './Bomb.js';
import { PowerUp, PowerUpManager } from './PowerUp.js';
import { webSocketClient } from '../network/WebSocketClient.js';

export class GameEngine {
    constructor() {
        this.gameMap = new GameMap();
        this.powerUpManager = new PowerUpManager();
        this.localEntities = new Map(); // Client-side entities for prediction
        this.lastUpdateTime = 0;
        this.gameStartTime = 0;
        this.isRunning = false;
        
        this.setupEventListeners();
    }

    /**
     * Setup event listeners for game events
     */
    setupEventListeners() {
        // Server events
        eventEmitter.on('game:started', (data) => {
            this.handleGameStart(data);
        });

        eventEmitter.on('game:ended', (data) => {
            this.handleGameEnd(data);
        });

        eventEmitter.on('game:restarted', (data) => {
            this.handleGameRestart(data);
        });

        eventEmitter.on('game:bomb-placed', (data) => {
            this.handleBombPlaced(data);
        });

        eventEmitter.on('game:explosion', (data) => {
            this.handleExplosion(data);
        });

        eventEmitter.on('game:explosion-end', (data) => {
            this.handleExplosionEnd(data);
        });

        eventEmitter.on('game:player-moved', (data) => {
            this.handlePlayerMoved(data);
        });
    }

    /**
     * Update game engine
     */
    update(deltaTime) {
        if (!this.isRunning) return;

        this.lastUpdateTime = Date.now();

        // Update local entities for smooth gameplay
        this.updateLocalEntities(deltaTime);

        // Update power-up manager
        this.powerUpManager.update(deltaTime);

        // Check for power-up collection
        this.checkPowerUpCollection();

        // Update game state
        this.updateGameState(deltaTime);
    }

    /**
     * Update local entities (client-side prediction)
     */
    updateLocalEntities(deltaTime) {
        const bombs = gameState.getBombs();

        // Update bomb timers locally for smooth countdown
        if (bombs && bombs instanceof Map) {
            for (const [bombId, bomb] of bombs) {
                if (bomb.timer > 0) {
                    bomb.timer = Math.max(0, bomb.timer - deltaTime);
                }
            }
        }

        // Update local player movement prediction
        this.updateLocalPlayerMovement(deltaTime);
    }

    /**
     * Update local player movement for responsiveness
     */
    updateLocalPlayerMovement(deltaTime) {
        const localPlayer = gameState.getLocalPlayer();
        if (!localPlayer || !localPlayer.isAlive) return;

        // Smooth movement interpolation could be added here
        // For now, we rely on server updates
    }

    /**
     * Check for power-up collection
     */
    checkPowerUpCollection() {
        const localPlayer = gameState.getLocalPlayer();
        if (!localPlayer || !localPlayer.isAlive) return;

        const powerups = gameState.getPowerups();
        const playerPos = { x: Math.round(localPlayer.x), y: Math.round(localPlayer.y) };

        for (const [powerupId, powerup] of powerups) {
            if (!powerup.isCollected && 
                powerup.x === playerPos.x && 
                powerup.y === playerPos.y) {
                
                // Collect power-up locally for immediate feedback
                powerup.collect(localPlayer.id);
                this.applyPowerUpEffect(localPlayer, powerup);
                
                // Remove from state
                gameState.removePowerup(powerupId);
                
                // Server will handle the authoritative collection
                break; // Only collect one per frame
            }
        }
    }

    /**
     * Apply power-up effect to player
     */
    applyPowerUpEffect(player, powerup) {
        switch (powerup.type) {
            case 'bomb':
                player.bombCapacity++;
                break;
            case 'flame':
                player.bombRange++;
                break;
            case 'speed':
                player.speed++;
                break;
        }

        // Update player in state
        gameState.updatePlayer(player.id, {
            bombCapacity: player.bombCapacity,
            bombRange: player.bombRange,
            speed: player.speed
        });

        console.log(`Power-up collected: ${powerup.type}`, player);
    }

    /**
     * Update overall game state
     */
    updateGameState(deltaTime) {
        // Check win conditions
        if (gameState.getGameState() === GAME_STATES.PLAYING) {
            this.checkWinConditions();
        }

        // Update game timer
        if (this.gameStartTime > 0) {
            const gameTime = Date.now() - this.gameStartTime;
            // Could add game time limit here
        }
    }

    /**
     * Check win conditions
     */
    checkWinConditions() {
        const players = gameState.getPlayers();
        const alivePlayers = Array.from(players.values()).filter(p => p.isAlive);

        if (alivePlayers.length <= 1) {
            // Game should end - server will handle this
            console.log('Win condition met, alive players:', alivePlayers.length);
        }
    }

    /**
     * Handle game start
     */
    handleGameStart(data) {
        console.log('Game engine: Game started', data);

        this.isRunning = true;
        this.gameStartTime = Date.now();

        // Initialize game map
        if (data.gameData && data.gameData.map) {
            console.log('Setting map data:', data.gameData.map.length, 'x', data.gameData.map[0].length);
            console.log('Map sample:', data.gameData.map[1]); // Show second row (not border)
            gameState.setMap(data.gameData.map);
        } else {
            console.error('No map data received!');
        }

        // Initialize players
        if (data.players) {
            console.log('Setting players:', data.players);
            gameState.setPlayers(data.players);
        }

        // Clear any existing entities
        gameState.setBombs([]);
        gameState.setExplosions([]);
        gameState.setPowerups([]);
        this.powerUpManager.clear();

        eventEmitter.emit('engine:game-started');
    }

    /**
     * Handle game end
     */
    handleGameEnd(data) {
        console.log('Game engine: Game ended', data);
        
        this.isRunning = false;
        gameState.setWinner(data.winner);
        
        eventEmitter.emit('engine:game-ended', data);
    }

    /**
     * Handle game restart
     */
    handleGameRestart(data) {
        console.log('Game engine: Game restarted', data);
        
        this.isRunning = false;
        this.gameStartTime = 0;
        
        // Reset game state
        gameState.resetGameState();
        gameState.setPlayers(data.players);
        
        // Clear entities
        this.powerUpManager.clear();
        this.localEntities.clear();
        
        eventEmitter.emit('engine:game-restarted');
    }

    /**
     * Handle bomb placement
     */
    handleBombPlaced(data) {
        console.log('Game engine: Bomb placed', data);
        
        const bomb = data.bomb;
        gameState.addBomb(bomb);
        
        eventEmitter.emit('engine:bomb-placed', bomb);
    }

    /**
     * Handle explosion
     */
    handleExplosion(data) {
        console.log('Game engine: Explosion', data);
        
        // Remove the bomb
        gameState.removeBomb(data.bombId);
        
        // Add explosions
        gameState.setExplosions(data.explosions);
        
        // Update map
        if (data.mapUpdate) {
            gameState.setMap(data.mapUpdate);
        }
        
        // Update power-ups
        if (data.powerups) {
            gameState.setPowerups(data.powerups);
        }
        
        // Update players
        if (data.players) {
            gameState.setPlayers(data.players);
        }
        
        eventEmitter.emit('engine:explosion', data);
    }

    /**
     * Handle explosion end
     */
    handleExplosionEnd(data) {
        console.log('Game engine: Explosion ended', data);
        
        // Clear explosions
        gameState.setExplosions([]);
        
        eventEmitter.emit('engine:explosion-ended');
    }

    /**
     * Handle player movement
     */
    handlePlayerMoved(data) {
        // Update player position
        gameState.updatePlayer(data.playerId, {
            x: data.x,
            y: data.y
        });
    }

    /**
     * Start a new game
     */
    startGame(gameData) {
        this.handleGameStart(gameData);
    }

    /**
     * End the current game
     */
    endGame(gameData) {
        this.handleGameEnd(gameData);
    }

    /**
     * Get game statistics
     */
    getGameStats() {
        return {
            isRunning: this.isRunning,
            gameTime: this.gameStartTime > 0 ? Date.now() - this.gameStartTime : 0,
            playerCount: gameState.getPlayerCount(),
            alivePlayerCount: gameState.getAlivePlayerCount(),
            bombCount: gameState.getBombs().size,
            powerupCount: this.powerUpManager.getActiveCount()
        };
    }

    /**
     * Reset the game engine
     */
    reset() {
        this.isRunning = false;
        this.gameStartTime = 0;
        this.lastUpdateTime = 0;
        this.localEntities.clear();
        this.powerUpManager.clear();
    }

    /**
     * Cleanup resources
     */
    cleanup() {
        this.reset();
        // Remove event listeners if needed
    }
}
