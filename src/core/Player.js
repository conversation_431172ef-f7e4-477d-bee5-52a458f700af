/**
 * Player Entity Class
 * Represents a player in the Bomberman game
 */

import { GAME_CONFIG, SPAWN_POSITIONS, POWERUP_TYPES } from '../config/constants.js';
import { clamp, isValidPosition } from '../utils/helpers.js';

export class Player {
    constructor(id, nickname, playerIndex = 0) {
        this.id = id;
        this.nickname = nickname;
        this.playerIndex = playerIndex;
        
        // Position
        this.x = SPAWN_POSITIONS[playerIndex]?.x || 1;
        this.y = SPAWN_POSITIONS[playerIndex]?.y || 1;
        this.targetX = this.x;
        this.targetY = this.y;
        
        // Game stats
        this.lives = GAME_CONFIG.INITIAL_LIVES;
        this.bombCapacity = GAME_CONFIG.INITIAL_BOMB_CAPACITY;
        this.bombRange = GAME_CONFIG.INITIAL_BOMB_RANGE;
        this.speed = GAME_CONFIG.INITIAL_SPEED;
        this.isAlive = true;
        
        // Power-ups collected
        this.powerups = [];
        
        // Movement state
        this.isMoving = false;
        this.moveStartTime = 0;
        this.moveDuration = 200; // Base movement duration in ms
        
        // Bomb state
        this.bombsPlaced = 0;
        this.lastBombTime = 0;
        
        // Visual state
        this.color = this.getPlayerColor();
        this.animationFrame = 0;
        this.lastAnimationTime = 0;
    }

    /**
     * Get the player's color based on their index
     */
    getPlayerColor() {
        const colors = ['#e74c3c', '#3498db', '#2ecc71', '#f39c12'];
        return colors[this.playerIndex % colors.length];
    }

    /**
     * Update player position and state
     */
    update(deltaTime) {
        // Update movement animation
        if (this.isMoving) {
            this.updateMovement(deltaTime);
        }
        
        // Update visual animations
        this.updateAnimation(deltaTime);
    }

    /**
     * Update movement interpolation
     */
    updateMovement(deltaTime) {
        const elapsed = Date.now() - this.moveStartTime;
        const progress = Math.min(elapsed / this.getMoveDuration(), 1);
        
        if (progress >= 1) {
            // Movement complete
            this.x = this.targetX;
            this.y = this.targetY;
            this.isMoving = false;
        } else {
            // Interpolate position
            const startX = this.x;
            const startY = this.y;
            this.x = startX + (this.targetX - startX) * progress;
            this.y = startY + (this.targetY - startY) * progress;
        }
    }

    /**
     * Update visual animations
     */
    updateAnimation(deltaTime) {
        this.lastAnimationTime += deltaTime;
        if (this.lastAnimationTime >= 500) { // 500ms per frame
            this.animationFrame = (this.animationFrame + 1) % 4;
            this.lastAnimationTime = 0;
        }
    }

    /**
     * Get movement duration based on speed
     */
    getMoveDuration() {
        return Math.max(100, this.moveDuration - (this.speed - 1) * 50);
    }

    /**
     * Attempt to move in a direction
     */
    move(direction, gameMap) {
        if (this.isMoving || !this.isAlive) {
            return false;
        }

        const newX = this.targetX + direction.x;
        const newY = this.targetY + direction.y;

        // Check bounds
        if (!isValidPosition(newX, newY)) {
            return false;
        }

        // Check collision with walls and blocks
        if (gameMap[newY][newX] === 'wall' || gameMap[newY][newX] === 'block') {
            return false;
        }

        // Start movement
        this.targetX = newX;
        this.targetY = newY;
        this.isMoving = true;
        this.moveStartTime = Date.now();

        return true;
    }

    /**
     * Set position directly (for network updates)
     */
    setPosition(x, y) {
        this.x = clamp(x, 0, GAME_CONFIG.MAP_WIDTH - 1);
        this.y = clamp(y, 0, GAME_CONFIG.MAP_HEIGHT - 1);
        this.targetX = this.x;
        this.targetY = this.y;
        this.isMoving = false;
    }

    /**
     * Get current grid position
     */
    getGridPosition() {
        return {
            x: Math.round(this.targetX),
            y: Math.round(this.targetY)
        };
    }

    /**
     * Check if player can place a bomb
     */
    canPlaceBomb() {
        if (!this.isAlive) return false;
        
        const now = Date.now();
        const timeSinceLastBomb = now - this.lastBombTime;
        
        return this.bombsPlaced < this.bombCapacity && timeSinceLastBomb >= 100;
    }

    /**
     * Place a bomb
     */
    placeBomb() {
        if (!this.canPlaceBomb()) return null;

        const gridPos = this.getGridPosition();
        this.bombsPlaced++;
        this.lastBombTime = Date.now();

        return {
            id: `bomb_${this.id}_${Date.now()}`,
            x: gridPos.x,
            y: gridPos.y,
            playerId: this.id,
            range: this.bombRange,
            timer: GAME_CONFIG.BOMB_TIMER,
            placedAt: Date.now()
        };
    }

    /**
     * Bomb exploded, decrease count
     */
    bombExploded() {
        this.bombsPlaced = Math.max(0, this.bombsPlaced - 1);
    }

    /**
     * Take damage
     */
    takeDamage() {
        if (!this.isAlive) return false;

        this.lives--;
        if (this.lives <= 0) {
            this.isAlive = false;
            return true; // Player died
        }
        return false; // Player survived
    }

    /**
     * Collect a power-up
     */
    collectPowerup(powerupType) {
        this.powerups.push(powerupType);

        switch (powerupType) {
            case POWERUP_TYPES.BOMB:
                this.bombCapacity++;
                break;
            case POWERUP_TYPES.FLAME:
                this.bombRange++;
                break;
            case POWERUP_TYPES.SPEED:
                this.speed++;
                break;
        }
    }

    /**
     * Reset player for new game
     */
    reset() {
        // Reset position
        this.x = SPAWN_POSITIONS[this.playerIndex]?.x || 1;
        this.y = SPAWN_POSITIONS[this.playerIndex]?.y || 1;
        this.targetX = this.x;
        this.targetY = this.y;
        
        // Reset stats
        this.lives = GAME_CONFIG.INITIAL_LIVES;
        this.bombCapacity = GAME_CONFIG.INITIAL_BOMB_CAPACITY;
        this.bombRange = GAME_CONFIG.INITIAL_BOMB_RANGE;
        this.speed = GAME_CONFIG.INITIAL_SPEED;
        this.isAlive = true;
        
        // Reset state
        this.powerups = [];
        this.isMoving = false;
        this.bombsPlaced = 0;
        this.lastBombTime = 0;
        this.animationFrame = 0;
        this.lastAnimationTime = 0;
    }

    /**
     * Get player data for serialization
     */
    serialize() {
        return {
            id: this.id,
            nickname: this.nickname,
            playerIndex: this.playerIndex,
            x: this.x,
            y: this.y,
            targetX: this.targetX,
            targetY: this.targetY,
            lives: this.lives,
            bombCapacity: this.bombCapacity,
            bombRange: this.bombRange,
            speed: this.speed,
            isAlive: this.isAlive,
            powerups: this.powerups,
            color: this.color,
            isMoving: this.isMoving
        };
    }

    /**
     * Create player from serialized data
     */
    static deserialize(data) {
        const player = new Player(data.id, data.nickname, data.playerIndex);
        
        player.x = data.x;
        player.y = data.y;
        player.targetX = data.targetX;
        player.targetY = data.targetY;
        player.lives = data.lives;
        player.bombCapacity = data.bombCapacity;
        player.bombRange = data.bombRange;
        player.speed = data.speed;
        player.isAlive = data.isAlive;
        player.powerups = data.powerups || [];
        player.isMoving = data.isMoving || false;
        
        return player;
    }

    /**
     * Check if player is at a specific grid position
     */
    isAtPosition(x, y) {
        const gridPos = this.getGridPosition();
        return gridPos.x === x && gridPos.y === y;
    }

    /**
     * Get player's current cell for collision detection
     */
    getCurrentCell() {
        return {
            x: Math.floor(this.x + 0.5),
            y: Math.floor(this.y + 0.5)
        };
    }

    /**
     * Check if player overlaps with a position
     */
    overlapsPosition(x, y, threshold = 0.3) {
        const dx = Math.abs(this.x - x);
        const dy = Math.abs(this.y - y);
        return dx < threshold && dy < threshold;
    }
}
