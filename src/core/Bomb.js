/**
 * Bomb Entity Class
 * Represents bombs in the Bomberman game
 */

import { GAME_CONFIG } from '../config/constants.js';
import { generateId } from '../utils/helpers.js';

export class Bomb {
    constructor(x, y, playerId, range = GAME_CONFIG.INITIAL_BOMB_RANGE) {
        this.id = generateId('bomb');
        this.x = x;
        this.y = y;
        this.playerId = playerId;
        this.range = range;
        this.timer = GAME_CONFIG.BOMB_TIMER;
        this.placedAt = Date.now();
        this.isExploded = false;
        
        // Visual state
        this.animationFrame = 0;
        this.lastAnimationTime = 0;
        this.pulseScale = 1.0;
        this.pulseDirection = 1;
    }

    /**
     * Update bomb state
     */
    update(deltaTime) {
        if (this.isExploded) return;

        // Update timer
        const elapsed = Date.now() - this.placedAt;
        this.timer = Math.max(0, GAME_CONFIG.BOMB_TIMER - elapsed);

        // Update visual animations
        this.updateAnimation(deltaTime);

        // Check if bomb should explode
        if (this.timer <= 0) {
            this.explode();
        }
    }

    /**
     * Update visual animations
     */
    updateAnimation(deltaTime) {
        this.lastAnimationTime += deltaTime;

        // Pulse animation - gets faster as timer decreases
        const pulseSpeed = this.getRemainingTimeRatio() * 500 + 100; // 100-600ms
        if (this.lastAnimationTime >= pulseSpeed) {
            this.pulseScale += this.pulseDirection * 0.1;
            if (this.pulseScale >= 1.2) {
                this.pulseScale = 1.2;
                this.pulseDirection = -1;
            } else if (this.pulseScale <= 0.8) {
                this.pulseScale = 0.8;
                this.pulseDirection = 1;
            }
            this.lastAnimationTime = 0;
        }

        // Animation frame for sprite cycling
        const frameSpeed = 200; // 200ms per frame
        if (this.lastAnimationTime >= frameSpeed) {
            this.animationFrame = (this.animationFrame + 1) % 4;
        }
    }

    /**
     * Get remaining time as a ratio (1.0 = full time, 0.0 = exploding)
     */
    getRemainingTimeRatio() {
        return this.timer / GAME_CONFIG.BOMB_TIMER;
    }

    /**
     * Get remaining time in seconds
     */
    getRemainingSeconds() {
        return Math.ceil(this.timer / 1000);
    }

    /**
     * Check if bomb is about to explode (last second)
     */
    isAboutToExplode() {
        return this.timer <= 1000;
    }

    /**
     * Explode the bomb
     */
    explode() {
        if (this.isExploded) return;
        
        this.isExploded = true;
        this.timer = 0;
        
        return this.createExplosionPattern();
    }

    /**
     * Create explosion pattern based on bomb range
     */
    createExplosionPattern() {
        const explosions = [];
        const directions = [
            { x: 0, y: 0 },   // Center
            { x: 1, y: 0 },   // Right
            { x: -1, y: 0 },  // Left
            { x: 0, y: 1 },   // Down
            { x: 0, y: -1 }   // Up
        ];

        for (const dir of directions) {
            const maxDistance = dir.x === 0 && dir.y === 0 ? 1 : this.range;
            
            for (let i = 0; i < maxDistance; i++) {
                const x = this.x + dir.x * i;
                const y = this.y + dir.y * i;

                explosions.push({
                    x: x,
                    y: y,
                    direction: dir,
                    distance: i,
                    isCenter: dir.x === 0 && dir.y === 0,
                    isEnd: i === maxDistance - 1
                });
            }
        }

        return explosions;
    }

    /**
     * Force explode the bomb (chain reaction)
     */
    forceExplode() {
        this.timer = 0;
        return this.explode();
    }

    /**
     * Check if bomb can be triggered by another explosion
     */
    canBeTriggered() {
        return !this.isExploded;
    }

    /**
     * Get bomb data for serialization
     */
    serialize() {
        return {
            id: this.id,
            x: this.x,
            y: this.y,
            playerId: this.playerId,
            range: this.range,
            timer: this.timer,
            placedAt: this.placedAt,
            isExploded: this.isExploded,
            animationFrame: this.animationFrame,
            pulseScale: this.pulseScale
        };
    }

    /**
     * Create bomb from serialized data
     */
    static deserialize(data) {
        const bomb = new Bomb(data.x, data.y, data.playerId, data.range);
        bomb.id = data.id;
        bomb.timer = data.timer;
        bomb.placedAt = data.placedAt;
        bomb.isExploded = data.isExploded;
        bomb.animationFrame = data.animationFrame || 0;
        bomb.pulseScale = data.pulseScale || 1.0;
        return bomb;
    }

    /**
     * Check if bomb is at a specific position
     */
    isAtPosition(x, y) {
        return this.x === x && this.y === y;
    }

    /**
     * Get visual properties for rendering
     */
    getVisualProperties() {
        return {
            scale: this.pulseScale,
            animationFrame: this.animationFrame,
            opacity: this.isAboutToExplode() ? 0.7 + Math.sin(Date.now() * 0.02) * 0.3 : 1.0,
            color: this.isAboutToExplode() ? '#ff4444' : '#2c3e50',
            borderColor: this.isAboutToExplode() ? '#ff0000' : '#e74c3c'
        };
    }
}

/**
 * Explosion Entity Class
 * Represents explosion effects
 */
export class Explosion {
    constructor(x, y, direction = { x: 0, y: 0 }, distance = 0) {
        this.id = generateId('explosion');
        this.x = x;
        this.y = y;
        this.direction = direction;
        this.distance = distance;
        this.isCenter = direction.x === 0 && direction.y === 0;
        this.createdAt = Date.now();
        this.duration = GAME_CONFIG.EXPLOSION_DURATION;
        
        // Visual state
        this.animationFrame = 0;
        this.lastAnimationTime = 0;
        this.scale = 0.5;
        this.opacity = 1.0;
    }

    /**
     * Update explosion animation
     */
    update(deltaTime) {
        const elapsed = Date.now() - this.createdAt;
        const progress = elapsed / this.duration;

        // Update scale animation
        if (progress < 0.3) {
            // Expand phase
            this.scale = 0.5 + (progress / 0.3) * 0.7; // 0.5 to 1.2
        } else if (progress < 0.7) {
            // Hold phase
            this.scale = 1.2;
        } else {
            // Fade phase
            this.scale = 1.2 - ((progress - 0.7) / 0.3) * 0.2; // 1.2 to 1.0
        }

        // Update opacity
        if (progress > 0.5) {
            this.opacity = 1.0 - ((progress - 0.5) / 0.5);
        }

        // Update animation frame
        this.lastAnimationTime += deltaTime;
        if (this.lastAnimationTime >= 50) { // Fast animation
            this.animationFrame = (this.animationFrame + 1) % 8;
            this.lastAnimationTime = 0;
        }

        return progress < 1.0; // Return false when animation is complete
    }

    /**
     * Check if explosion is finished
     */
    isFinished() {
        const elapsed = Date.now() - this.createdAt;
        return elapsed >= this.duration;
    }

    /**
     * Get explosion data for serialization
     */
    serialize() {
        return {
            id: this.id,
            x: this.x,
            y: this.y,
            direction: this.direction,
            distance: this.distance,
            isCenter: this.isCenter,
            createdAt: this.createdAt,
            duration: this.duration
        };
    }

    /**
     * Create explosion from serialized data
     */
    static deserialize(data) {
        const explosion = new Explosion(data.x, data.y, data.direction, data.distance);
        explosion.id = data.id;
        explosion.isCenter = data.isCenter;
        explosion.createdAt = data.createdAt;
        explosion.duration = data.duration;
        return explosion;
    }

    /**
     * Get visual properties for rendering
     */
    getVisualProperties() {
        return {
            scale: this.scale,
            opacity: this.opacity,
            animationFrame: this.animationFrame,
            isCenter: this.isCenter,
            direction: this.direction
        };
    }
}
