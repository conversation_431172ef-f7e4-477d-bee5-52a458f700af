/**
 * PowerUp Entity Class
 * Represents power-ups in the Bomberman game
 */

import { POWERUP_TYPES, GAME_CONFIG } from '../config/constants.js';
import { generateId } from '../utils/helpers.js';

export class PowerUp {
    constructor(x, y, type) {
        this.id = generateId('powerup');
        this.x = x;
        this.y = y;
        this.type = type;
        this.createdAt = Date.now();
        this.isCollected = false;
        
        // Visual state
        this.animationFrame = 0;
        this.lastAnimationTime = 0;
        this.glowIntensity = 0.5;
        this.glowDirection = 1;
        this.bobOffset = 0;
        this.bobDirection = 1;
    }

    /**
     * Update power-up animations
     */
    update(deltaTime) {
        if (this.isCollected) return;

        this.updateAnimation(deltaTime);
    }

    /**
     * Update visual animations
     */
    updateAnimation(deltaTime) {
        this.lastAnimationTime += deltaTime;

        // Glow animation
        if (this.lastAnimationTime >= 50) {
            this.glowIntensity += this.glowDirection * 0.05;
            if (this.glowIntensity >= 1.0) {
                this.glowIntensity = 1.0;
                this.glowDirection = -1;
            } else if (this.glowIntensity <= 0.3) {
                this.glowIntensity = 0.3;
                this.glowDirection = 1;
            }

            // Bob animation (floating effect)
            this.bobOffset += this.bobDirection * 0.5;
            if (this.bobOffset >= 3) {
                this.bobOffset = 3;
                this.bobDirection = -1;
            } else if (this.bobOffset <= -3) {
                this.bobOffset = -3;
                this.bobDirection = 1;
            }

            this.lastAnimationTime = 0;
        }

        // Rotation animation frame
        if (this.lastAnimationTime >= 100) {
            this.animationFrame = (this.animationFrame + 1) % 8;
        }
    }

    /**
     * Collect the power-up
     */
    collect(playerId) {
        if (this.isCollected) return false;

        this.isCollected = true;
        this.collectedBy = playerId;
        this.collectedAt = Date.now();

        return true;
    }

    /**
     * Check if power-up can be collected
     */
    canBeCollected() {
        return !this.isCollected;
    }

    /**
     * Get power-up effect description
     */
    getEffectDescription() {
        switch (this.type) {
            case POWERUP_TYPES.BOMB:
                return '+1 Bomb Capacity';
            case POWERUP_TYPES.FLAME:
                return '+1 Explosion Range';
            case POWERUP_TYPES.SPEED:
                return '+1 Movement Speed';
            default:
                return 'Unknown Effect';
        }
    }

    /**
     * Get power-up color based on type
     */
    getColor() {
        switch (this.type) {
            case POWERUP_TYPES.BOMB:
                return '#9b59b6'; // Purple
            case POWERUP_TYPES.FLAME:
                return '#e74c3c'; // Red
            case POWERUP_TYPES.SPEED:
                return '#2ecc71'; // Green
            default:
                return '#95a5a6'; // Gray
        }
    }

    /**
     * Get power-up icon/symbol
     */
    getIcon() {
        switch (this.type) {
            case POWERUP_TYPES.BOMB:
                return '💣';
            case POWERUP_TYPES.FLAME:
                return '🔥';
            case POWERUP_TYPES.SPEED:
                return '⚡';
            default:
                return '?';
        }
    }

    /**
     * Check if power-up is at a specific position
     */
    isAtPosition(x, y) {
        return this.x === x && this.y === y;
    }

    /**
     * Get age of power-up in milliseconds
     */
    getAge() {
        return Date.now() - this.createdAt;
    }

    /**
     * Check if power-up should expire (optional feature)
     */
    shouldExpire(maxAge = 30000) { // 30 seconds default
        return this.getAge() > maxAge;
    }

    /**
     * Get visual properties for rendering
     */
    getVisualProperties() {
        return {
            color: this.getColor(),
            icon: this.getIcon(),
            glowIntensity: this.glowIntensity,
            bobOffset: this.bobOffset,
            animationFrame: this.animationFrame,
            scale: 1.0 + Math.sin(this.getAge() * 0.005) * 0.1, // Subtle breathing effect
            opacity: this.isCollected ? 0 : 1
        };
    }

    /**
     * Get power-up data for serialization
     */
    serialize() {
        return {
            id: this.id,
            x: this.x,
            y: this.y,
            type: this.type,
            createdAt: this.createdAt,
            isCollected: this.isCollected,
            collectedBy: this.collectedBy,
            collectedAt: this.collectedAt
        };
    }

    /**
     * Create power-up from serialized data
     */
    static deserialize(data) {
        const powerup = new PowerUp(data.x, data.y, data.type);
        powerup.id = data.id;
        powerup.createdAt = data.createdAt;
        powerup.isCollected = data.isCollected || false;
        powerup.collectedBy = data.collectedBy;
        powerup.collectedAt = data.collectedAt;
        return powerup;
    }

    /**
     * Create a random power-up type
     */
    static createRandom(x, y) {
        const types = Object.values(POWERUP_TYPES);
        const randomType = types[Math.floor(Math.random() * types.length)];
        return new PowerUp(x, y, randomType);
    }

    /**
     * Create power-up with weighted probability
     */
    static createWeighted(x, y, weights = null) {
        const defaultWeights = {
            [POWERUP_TYPES.BOMB]: 0.4,   // 40% chance
            [POWERUP_TYPES.FLAME]: 0.4,  // 40% chance
            [POWERUP_TYPES.SPEED]: 0.2   // 20% chance
        };

        const finalWeights = weights || defaultWeights;
        const random = Math.random();
        let cumulative = 0;

        for (const [type, weight] of Object.entries(finalWeights)) {
            cumulative += weight;
            if (random <= cumulative) {
                return new PowerUp(x, y, type);
            }
        }

        // Fallback to bomb if something goes wrong
        return new PowerUp(x, y, POWERUP_TYPES.BOMB);
    }
}

/**
 * PowerUp Manager Class
 * Manages all power-ups in the game
 */
export class PowerUpManager {
    constructor() {
        this.powerups = new Map();
        this.spawnQueue = [];
    }

    /**
     * Add a power-up to the manager
     */
    addPowerUp(powerup) {
        this.powerups.set(powerup.id, powerup);
    }

    /**
     * Remove a power-up from the manager
     */
    removePowerUp(powerupId) {
        return this.powerups.delete(powerupId);
    }

    /**
     * Get power-up by ID
     */
    getPowerUp(powerupId) {
        return this.powerups.get(powerupId);
    }

    /**
     * Get all power-ups
     */
    getAllPowerUps() {
        return Array.from(this.powerups.values());
    }

    /**
     * Get power-ups at a specific position
     */
    getPowerUpsAtPosition(x, y) {
        return this.getAllPowerUps().filter(powerup => 
            powerup.isAtPosition(x, y) && !powerup.isCollected
        );
    }

    /**
     * Update all power-ups
     */
    update(deltaTime) {
        for (const powerup of this.powerups.values()) {
            powerup.update(deltaTime);
        }

        // Remove expired power-ups (optional)
        this.removeExpiredPowerUps();
    }

    /**
     * Remove expired power-ups
     */
    removeExpiredPowerUps(maxAge = 60000) { // 60 seconds
        const toRemove = [];
        for (const [id, powerup] of this.powerups) {
            if (powerup.shouldExpire(maxAge)) {
                toRemove.push(id);
            }
        }
        
        toRemove.forEach(id => this.removePowerUp(id));
        return toRemove.length;
    }

    /**
     * Spawn power-up at position with chance
     */
    trySpawnPowerUp(x, y, chance = GAME_CONFIG.POWERUP_SPAWN_CHANCE) {
        if (Math.random() < chance) {
            const powerup = PowerUp.createWeighted(x, y);
            this.addPowerUp(powerup);
            return powerup;
        }
        return null;
    }

    /**
     * Clear all power-ups
     */
    clear() {
        this.powerups.clear();
        this.spawnQueue = [];
    }

    /**
     * Get power-up count
     */
    getCount() {
        return this.powerups.size;
    }

    /**
     * Get active (uncollected) power-up count
     */
    getActiveCount() {
        return this.getAllPowerUps().filter(p => !p.isCollected).length;
    }
}
