/**
 * Game State Management
 * Centralized state management for the Bomberman game
 */

import { globalState } from '../../mini-framework/src/framework.js';
import { GAME_CONFIG, GAME_STATES, CELL_TYPES } from '../config/constants.js';
import { create2DArray } from '../utils/helpers.js';

export class GameState {
    constructor() {
        this.initializeState();
        this.setupStateSubscriptions();
    }

    initializeState() {
        // Initialize global game state
        globalState.set('gameState', GAME_STATES.LOADING);
        globalState.set('playerId', null);
        globalState.set('roomId', null);
        globalState.set('nickname', '');
        
        // Player data
        globalState.set('players', new Map());
        globalState.set('localPlayer', null);
        
        // Game world
        globalState.set('map', create2DArray(GAME_CONFIG.MAP_WIDTH, GAME_CONFIG.MAP_HEIGHT, CELL_TYPES.EMPTY));
        globalState.set('bombs', new Map());
        globalState.set('explosions', new Map());
        globalState.set('powerups', new Map());
        
        // UI state
        globalState.set('chatMessages', []);
        globalState.set('countdown', 0);
        globalState.set('winner', null);
        globalState.set('showPerformanceMonitor', false);
        
        // Performance tracking
        globalState.set('performance', {
            fps: 60,
            frameTime: 16.67,
            lastFrameTime: 0,
            frameCount: 0,
            frameTimes: []
        });
        
        // Network state
        globalState.set('connected', false);
        globalState.set('connectionError', null);
    }

    setupStateSubscriptions() {
        // Subscribe to game state changes for debugging
        globalState.subscribe('gameState', (newState) => {
            console.log('Game state changed to:', newState);
        });

        // Subscribe to player changes
        globalState.subscribe('players', (players) => {
            console.log('Players updated:', players.size, 'players');
        });
    }

    // Game state getters
    getGameState() {
        return globalState.get('gameState');
    }

    getPlayerId() {
        return globalState.get('playerId');
    }

    getPlayers() {
        return globalState.get('players');
    }

    getLocalPlayer() {
        return globalState.get('localPlayer');
    }

    getMap() {
        return globalState.get('map');
    }

    getBombs() {
        return globalState.get('bombs');
    }

    getExplosions() {
        return globalState.get('explosions');
    }

    getPowerups() {
        return globalState.get('powerups');
    }

    getChatMessages() {
        return globalState.get('chatMessages');
    }

    getPerformance() {
        return globalState.get('performance');
    }

    // Game state setters
    setGameState(state) {
        globalState.set('gameState', state);
    }

    setPlayerId(id) {
        globalState.set('playerId', id);
    }

    setNickname(nickname) {
        globalState.set('nickname', nickname);
    }

    setRoomId(roomId) {
        globalState.set('roomId', roomId);
    }

    setPlayers(players) {
        const playersMap = new Map();
        if (Array.isArray(players)) {
            players.forEach(player => {
                playersMap.set(player.id, player);
            });
        }
        globalState.set('players', playersMap);
        
        // Update local player reference
        const playerId = this.getPlayerId();
        if (playerId && playersMap.has(playerId)) {
            globalState.set('localPlayer', playersMap.get(playerId));
        }
    }

    updatePlayer(playerId, playerData) {
        const players = this.getPlayers();
        if (players.has(playerId)) {
            const updatedPlayer = { ...players.get(playerId), ...playerData };
            players.set(playerId, updatedPlayer);
            globalState.set('players', new Map(players));
            
            // Update local player if it's the current player
            if (playerId === this.getPlayerId()) {
                globalState.set('localPlayer', updatedPlayer);
            }
        }
    }

    removePlayer(playerId) {
        const players = this.getPlayers();
        players.delete(playerId);
        globalState.set('players', new Map(players));
    }

    setMap(mapData) {
        globalState.set('map', mapData);
    }

    updateMapCell(x, y, cellType) {
        const map = this.getMap();
        if (map[y] && map[y][x] !== undefined) {
            map[y][x] = cellType;
            globalState.set('map', [...map]);
        }
    }

    setBombs(bombs) {
        const bombsMap = new Map();
        if (Array.isArray(bombs)) {
            bombs.forEach(bomb => {
                bombsMap.set(bomb.id, bomb);
            });
        }
        globalState.set('bombs', bombsMap);
    }

    addBomb(bomb) {
        const bombs = this.getBombs();
        bombs.set(bomb.id, bomb);
        globalState.set('bombs', new Map(bombs));
    }

    removeBomb(bombId) {
        const bombs = this.getBombs();
        bombs.delete(bombId);
        globalState.set('bombs', new Map(bombs));
    }

    setExplosions(explosions) {
        const explosionsMap = new Map();
        if (Array.isArray(explosions)) {
            explosions.forEach((explosion, index) => {
                explosionsMap.set(`explosion_${index}`, explosion);
            });
        }
        globalState.set('explosions', explosionsMap);
    }

    addExplosion(explosionId, explosion) {
        const explosions = this.getExplosions();
        explosions.set(explosionId, explosion);
        globalState.set('explosions', new Map(explosions));
    }

    removeExplosion(explosionId) {
        const explosions = this.getExplosions();
        explosions.delete(explosionId);
        globalState.set('explosions', new Map(explosions));
    }

    setPowerups(powerups) {
        const powerupsMap = new Map();
        if (Array.isArray(powerups)) {
            powerups.forEach(powerup => {
                powerupsMap.set(powerup.id, powerup);
            });
        }
        globalState.set('powerups', powerupsMap);
    }

    addPowerup(powerup) {
        const powerups = this.getPowerups();
        powerups.set(powerup.id, powerup);
        globalState.set('powerups', new Map(powerups));
    }

    removePowerup(powerupId) {
        const powerups = this.getPowerups();
        powerups.delete(powerupId);
        globalState.set('powerups', new Map(powerups));
    }

    addChatMessage(message) {
        const messages = this.getChatMessages();
        const newMessages = [...messages, message];
        
        // Keep only last 50 messages
        if (newMessages.length > 50) {
            newMessages.splice(0, newMessages.length - 50);
        }
        
        globalState.set('chatMessages', newMessages);
    }

    setChatHistory(messages) {
        globalState.set('chatMessages', messages || []);
    }

    setCountdown(seconds) {
        globalState.set('countdown', seconds);
    }

    setWinner(winner) {
        globalState.set('winner', winner);
    }

    setConnected(connected) {
        globalState.set('connected', connected);
    }

    setConnectionError(error) {
        globalState.set('connectionError', error);
    }

    updatePerformance(fps, frameTime) {
        const perf = this.getPerformance();
        const frameTimes = [...perf.frameTimes, frameTime];
        
        // Keep only last 60 frame times for averaging
        if (frameTimes.length > GAME_CONFIG.PERFORMANCE_SAMPLE_SIZE) {
            frameTimes.shift();
        }
        
        globalState.set('performance', {
            ...perf,
            fps: Math.round(fps),
            frameTime: Math.round(frameTime * 100) / 100,
            frameCount: perf.frameCount + 1,
            frameTimes: frameTimes
        });
    }

    togglePerformanceMonitor() {
        const current = globalState.get('showPerformanceMonitor');
        globalState.set('showPerformanceMonitor', !current);
    }

    // Utility methods
    isLocalPlayer(playerId) {
        return playerId === this.getPlayerId();
    }

    getPlayerCount() {
        return this.getPlayers().size;
    }

    getAlivePlayerCount() {
        return Array.from(this.getPlayers().values()).filter(p => p.isAlive).length;
    }

    isGameInProgress() {
        return this.getGameState() === GAME_STATES.PLAYING;
    }

    canPlaceBomb() {
        const localPlayer = this.getLocalPlayer();
        if (!localPlayer || !localPlayer.isAlive) return false;
        
        const bombs = this.getBombs();
        const playerBombs = Array.from(bombs.values()).filter(b => b.playerId === localPlayer.id);
        
        return playerBombs.length < localPlayer.bombCapacity;
    }

    // Reset state for new game
    resetGameState() {
        this.setGameState(GAME_STATES.LOBBY);
        globalState.set('bombs', new Map());
        globalState.set('explosions', new Map());
        globalState.set('powerups', new Map());
        globalState.set('winner', null);
        globalState.set('countdown', 0);
    }
}

// Create singleton instance
export const gameState = new GameState();
